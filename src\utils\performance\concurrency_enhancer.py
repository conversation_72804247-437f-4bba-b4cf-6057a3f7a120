#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
并发增强器

提供高级并发处理、负载均衡和性能优化功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import multiprocessing
import queue
import threading
import time
import weakref
from concurrent.futures import Future, ProcessPoolExecutor, ThreadPoolExecutor
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from ..logger import get_logger

logger = get_logger("ConcurrencyEnhancer")


class ExecutionMode(Enum):
    """执行模式枚举"""

    ASYNC = "async"
    THREAD = "thread"
    PROCESS = "process"
    HYBRID = "hybrid"


class LoadBalanceStrategy(Enum):
    """负载均衡策略枚举"""

    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    WEIGHTED = "weighted"
    ADAPTIVE = "adaptive"


@dataclass
class WorkerMetrics:
    """工作器指标"""

    worker_id: str
    active_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    avg_execution_time: float = 0.0
    last_activity: float = field(default_factory=time.time)
    cpu_usage: float = 0.0
    memory_usage: float = 0.0

    @property
    def load_score(self) -> float:
        """负载评分（越低越好）"""
        return self.active_tasks + (self.avg_execution_time * 0.1)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "worker_id": self.worker_id,
            "active_tasks": self.active_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "avg_execution_time": self.avg_execution_time,
            "last_activity": self.last_activity,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "load_score": self.load_score,
        }


class AdaptiveExecutor:
    """
    自适应执行器

    根据任务特性和系统负载自动选择最优执行方式
    """

    def __init__(self, max_workers: int = None):
        """初始化自适应执行器"""
        self.logger = get_logger(self.__class__.__name__)

        # 计算默认工作器数量
        if max_workers is None:
            max_workers = min(32, (multiprocessing.cpu_count() or 1) + 4)

        self.max_workers = max_workers

        # 执行器池
        self._thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self._process_pool = ProcessPoolExecutor(max_workers=max_workers // 2)

        # 工作器指标
        self._worker_metrics: Dict[str, WorkerMetrics] = {}
        self._metrics_lock = threading.Lock()

        # 负载均衡器
        self._load_balancer = LoadBalancer(LoadBalanceStrategy.ADAPTIVE)

        # 任务历史统计
        self._task_history: Dict[str, List[float]] = {}  # 函数名 -> 执行时间列表

        # 自适应配置
        self._cpu_bound_threshold = 0.1  # CPU密集型任务阈值（秒）
        self._io_bound_threshold = 1.0  # IO密集型任务阈值（秒）

        self.logger.info(f"自适应执行器初始化完成，最大工作器数: {max_workers}")

    async def submit_adaptive(self, func: Callable, *args, **kwargs) -> Any:
        """自适应提交任务"""
        func_name = f"{func.__module__}.{func.__name__}"

        # 分析任务特性
        execution_mode = self._analyze_task_characteristics(func_name, func)

        # 根据执行模式提交任务
        if execution_mode == ExecutionMode.ASYNC:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                # 包装为协程
                return await asyncio.get_event_loop().run_in_executor(None, func, *args)

        elif execution_mode == ExecutionMode.THREAD:
            return await self._submit_to_thread_pool(func, *args, **kwargs)

        elif execution_mode == ExecutionMode.PROCESS:
            return await self._submit_to_process_pool(func, *args, **kwargs)

        else:  # HYBRID
            return await self._submit_hybrid(func, *args, **kwargs)

    def _analyze_task_characteristics(
        self, func_name: str, func: Callable
    ) -> ExecutionMode:
        """分析任务特性"""
        # 检查历史执行时间
        if func_name in self._task_history:
            avg_time = sum(self._task_history[func_name]) / len(
                self._task_history[func_name]
            )

            if avg_time < self._cpu_bound_threshold:
                return ExecutionMode.ASYNC
            elif avg_time > self._io_bound_threshold:
                return ExecutionMode.PROCESS
            else:
                return ExecutionMode.THREAD

        # 基于函数特征的启发式分析
        if asyncio.iscoroutinefunction(func):
            return ExecutionMode.ASYNC

        # 检查函数名称中的关键词
        func_str = str(func)
        if any(
            keyword in func_str.lower() for keyword in ["io", "file", "network", "http"]
        ):
            return ExecutionMode.THREAD
        elif any(
            keyword in func_str.lower()
            for keyword in ["compute", "calculate", "process"]
        ):
            return ExecutionMode.PROCESS

        # 默认使用线程池
        return ExecutionMode.THREAD

    async def _submit_to_thread_pool(self, func: Callable, *args, **kwargs) -> Any:
        """提交到线程池"""
        loop = asyncio.get_event_loop()
        start_time = time.time()

        try:
            result = await loop.run_in_executor(self._thread_pool, func, *args)
            execution_time = time.time() - start_time
            self._record_execution_time(
                f"{func.__module__}.{func.__name__}", execution_time
            )
            return result
        except Exception as e:
            self.logger.error(f"线程池执行失败: {e}")
            raise

    async def _submit_to_process_pool(self, func: Callable, *args, **kwargs) -> Any:
        """提交到进程池"""
        loop = asyncio.get_event_loop()
        start_time = time.time()

        try:
            result = await loop.run_in_executor(self._process_pool, func, *args)
            execution_time = time.time() - start_time
            self._record_execution_time(
                f"{func.__module__}.{func.__name__}", execution_time
            )
            return result
        except Exception as e:
            self.logger.error(f"进程池执行失败: {e}")
            raise

    async def _submit_hybrid(self, func: Callable, *args, **kwargs) -> Any:
        """混合模式提交"""
        # 根据当前系统负载选择执行方式
        thread_load = self._get_pool_load(self._thread_pool)
        process_load = self._get_pool_load(self._process_pool)

        if thread_load < process_load:
            return await self._submit_to_thread_pool(func, *args, **kwargs)
        else:
            return await self._submit_to_process_pool(func, *args, **kwargs)

    def _get_pool_load(self, pool) -> float:
        """获取池负载"""
        # 简化实现，实际中可以获取更详细的负载信息
        if hasattr(pool, "_threads"):
            return len(pool._threads) / pool._max_workers
        elif hasattr(pool, "_processes"):
            return len(pool._processes) / pool._max_workers
        return 0.5  # 默认中等负载

    def _record_execution_time(self, func_name: str, execution_time: float):
        """记录执行时间"""
        if func_name not in self._task_history:
            self._task_history[func_name] = []

        self._task_history[func_name].append(execution_time)

        # 限制历史记录数量
        if len(self._task_history[func_name]) > 100:
            self._task_history[func_name] = self._task_history[func_name][-50:]

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            "thread_pool_size": self._thread_pool._max_workers,
            "process_pool_size": self._process_pool._max_workers,
            "task_history_count": len(self._task_history),
            "avg_execution_times": {},
        }

        for func_name, times in self._task_history.items():
            if times:
                stats["avg_execution_times"][func_name] = {
                    "avg": sum(times) / len(times),
                    "min": min(times),
                    "max": max(times),
                    "count": len(times),
                }

        return stats

    def shutdown(self):
        """关闭执行器"""
        self._thread_pool.shutdown(wait=True)
        self._process_pool.shutdown(wait=True)
        self.logger.info("自适应执行器已关闭")


class LoadBalancer:
    """
    负载均衡器

    在多个工作器之间分配任务
    """

    def __init__(self, strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN):
        """初始化负载均衡器"""
        self.strategy = strategy
        self._workers: List[str] = []
        self._worker_weights: Dict[str, float] = {}
        self._current_index = 0
        self._lock = threading.Lock()

        self.logger = get_logger(self.__class__.__name__)

    def register_worker(self, worker_id: str, weight: float = 1.0):
        """注册工作器"""
        with self._lock:
            if worker_id not in self._workers:
                self._workers.append(worker_id)
                self._worker_weights[worker_id] = weight
                self.logger.debug(f"注册工作器: {worker_id}, 权重: {weight}")

    def unregister_worker(self, worker_id: str):
        """注销工作器"""
        with self._lock:
            if worker_id in self._workers:
                self._workers.remove(worker_id)
                self._worker_weights.pop(worker_id, None)
                self.logger.debug(f"注销工作器: {worker_id}")

    def select_worker(
        self, worker_metrics: Dict[str, WorkerMetrics] = None
    ) -> Optional[str]:
        """选择工作器"""
        with self._lock:
            if not self._workers:
                return None

            if self.strategy == LoadBalanceStrategy.ROUND_ROBIN:
                return self._round_robin_select()

            elif self.strategy == LoadBalanceStrategy.LEAST_LOADED:
                return self._least_loaded_select(worker_metrics)

            elif self.strategy == LoadBalanceStrategy.WEIGHTED:
                return self._weighted_select()

            elif self.strategy == LoadBalanceStrategy.ADAPTIVE:
                return self._adaptive_select(worker_metrics)

            else:
                return self._round_robin_select()

    def _round_robin_select(self) -> str:
        """轮询选择"""
        worker = self._workers[self._current_index]
        self._current_index = (self._current_index + 1) % len(self._workers)
        return worker

    def _least_loaded_select(self, worker_metrics: Dict[str, WorkerMetrics]) -> str:
        """最少负载选择"""
        if not worker_metrics:
            return self._round_robin_select()

        min_load = float("inf")
        selected_worker = self._workers[0]

        for worker_id in self._workers:
            if worker_id in worker_metrics:
                load = worker_metrics[worker_id].load_score
                if load < min_load:
                    min_load = load
                    selected_worker = worker_id

        return selected_worker

    def _weighted_select(self) -> str:
        """加权选择"""
        import random

        total_weight = sum(self._worker_weights.get(w, 1.0) for w in self._workers)
        random_value = random.uniform(0, total_weight)

        current_weight = 0
        for worker_id in self._workers:
            current_weight += self._worker_weights.get(worker_id, 1.0)
            if random_value <= current_weight:
                return worker_id

        return self._workers[-1]  # 备选

    def _adaptive_select(self, worker_metrics: Dict[str, WorkerMetrics]) -> str:
        """自适应选择"""
        if not worker_metrics:
            return self._weighted_select()

        # 综合考虑负载和权重
        best_score = float("inf")
        selected_worker = self._workers[0]

        for worker_id in self._workers:
            if worker_id in worker_metrics:
                metrics = worker_metrics[worker_id]
                weight = self._worker_weights.get(worker_id, 1.0)

                # 计算综合评分（负载/权重，越小越好）
                score = metrics.load_score / weight

                if score < best_score:
                    best_score = score
                    selected_worker = worker_id

        return selected_worker


class ConcurrentBatchProcessor:
    """
    并发批处理器

    高效处理大批量任务
    """

    def __init__(self, batch_size: int = 100, max_concurrent_batches: int = 10):
        """初始化批处理器"""
        self.batch_size = batch_size
        self.max_concurrent_batches = max_concurrent_batches
        self.logger = get_logger(self.__class__.__name__)

        # 批处理队列
        self._batch_queue: asyncio.Queue = asyncio.Queue()
        self._processing_semaphore = asyncio.Semaphore(max_concurrent_batches)

        # 统计信息
        self.processed_items = 0
        self.failed_items = 0
        self.total_batches = 0

    async def process_items(
        self,
        items: List[Any],
        processor: Callable[[Any], Awaitable[Any]],
        progress_callback: Optional[Callable] = None,
    ) -> List[Any]:
        """处理项目列表"""
        if not items:
            return []

        # 分批处理
        batches = [
            items[i : i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]

        self.total_batches = len(batches)
        self.logger.info(f"开始批处理: {len(items)}个项目，{self.total_batches}个批次")

        # 并发处理批次
        batch_tasks = []
        for i, batch in enumerate(batches):
            task = self._process_batch(batch, processor, i, progress_callback)
            batch_tasks.append(task)

        # 等待所有批次完成
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

        # 合并结果
        results = []
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                self.logger.error(f"批处理失败: {batch_result}")
                continue

            if isinstance(batch_result, list):
                results.extend(batch_result)

        self.logger.info(f"批处理完成: 成功 {self.processed_items}，失败 {self.failed_items}")
        return results

    async def _process_batch(
        self,
        batch: List[Any],
        processor: Callable[[Any], Awaitable[Any]],
        batch_index: int,
        progress_callback: Optional[Callable] = None,
    ) -> List[Any]:
        """处理单个批次"""
        async with self._processing_semaphore:
            batch_results = []

            # 并发处理批次内的项目
            item_tasks = []
            for item in batch:
                task = self._process_item(item, processor)
                item_tasks.append(task)

            item_results = await asyncio.gather(*item_tasks, return_exceptions=True)

            # 处理结果
            for result in item_results:
                if isinstance(result, Exception):
                    self.failed_items += 1
                    self.logger.warning(f"项目处理失败: {result}")
                else:
                    self.processed_items += 1
                    batch_results.append(result)

            # 进度回调
            if progress_callback:
                try:
                    await progress_callback(batch_index + 1, self.total_batches)
                except Exception as e:
                    self.logger.warning(f"进度回调失败: {e}")

            return batch_results

    async def _process_item(
        self, item: Any, processor: Callable[[Any], Awaitable[Any]]
    ) -> Any:
        """处理单个项目"""
        try:
            return await processor(item)
        except Exception as e:
            self.logger.debug(f"项目处理异常: {e}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "batch_size": self.batch_size,
            "max_concurrent_batches": self.max_concurrent_batches,
            "processed_items": self.processed_items,
            "failed_items": self.failed_items,
            "total_batches": self.total_batches,
            "success_rate": (
                self.processed_items / max(1, self.processed_items + self.failed_items)
            )
            * 100,
        }


# 全局并发增强器实例
_adaptive_executor = None
_batch_processor = None
_executor_lock = threading.Lock()


def get_adaptive_executor() -> AdaptiveExecutor:
    """获取全局自适应执行器实例"""
    global _adaptive_executor
    with _executor_lock:
        if _adaptive_executor is None:
            _adaptive_executor = AdaptiveExecutor()
        return _adaptive_executor


def get_batch_processor() -> ConcurrentBatchProcessor:
    """获取全局批处理器实例"""
    global _batch_processor
    with _executor_lock:
        if _batch_processor is None:
            _batch_processor = ConcurrentBatchProcessor()
        return _batch_processor


# 装饰器函数
def adaptive_execution(func):
    """自适应执行装饰器"""

    async def wrapper(*args, **kwargs):
        executor = get_adaptive_executor()
        return await executor.submit_adaptive(func, *args, **kwargs)

    return wrapper


def batch_process(batch_size: int = 100):
    """批处理装饰器"""

    def decorator(func):
        async def wrapper(items: List[Any], *args, **kwargs):
            processor = get_batch_processor()
            processor.batch_size = batch_size

            async def item_processor(item):
                return await func(item, *args, **kwargs)

            return await processor.process_items(items, item_processor)

        return wrapper

    return decorator
