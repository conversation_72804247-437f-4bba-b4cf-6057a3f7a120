#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
故障检测器

提供系统健康监控、故障检测和自动恢复功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import json
import threading
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import psutil

from ..logger import get_logger
from ..unified_types import TaskStatus, TaskType

logger = get_logger("FaultDetector")


class HealthStatus(Enum):
    """健康状态枚举"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class ComponentType(Enum):
    """组件类型枚举"""

    EVENT_LOOP = "event_loop"
    TASK_QUEUE = "task_queue"
    MEMORY = "memory"
    CPU = "cpu"
    DISK = "disk"
    DATABASE = "database"
    THREAD_POOL = "thread_pool"
    PROCESS_POOL = "process_pool"


@dataclass
class HealthMetric:
    """健康指标"""

    name: str
    value: float
    threshold_warning: float
    threshold_critical: float
    unit: str = ""
    timestamp: float = field(default_factory=time.time)

    @property
    def status(self) -> HealthStatus:
        """根据阈值判断状态"""
        if self.value >= self.threshold_critical:
            return HealthStatus.CRITICAL
        elif self.value >= self.threshold_warning:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY


@dataclass
class ComponentHealth:
    """组件健康状态"""

    component_type: ComponentType
    status: HealthStatus
    metrics: Dict[str, HealthMetric] = field(default_factory=dict)
    last_check: float = field(default_factory=time.time)
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "component_type": self.component_type.value,
            "status": self.status.value,
            "metrics": {
                name: {
                    "value": metric.value,
                    "threshold_warning": metric.threshold_warning,
                    "threshold_critical": metric.threshold_critical,
                    "unit": metric.unit,
                    "status": metric.status.value,
                }
                for name, metric in self.metrics.items()
            },
            "last_check": self.last_check,
            "error_message": self.error_message,
        }


class FaultDetector:
    """
    故障检测器

    监控系统各组件的健康状态，检测故障并触发恢复机制
    """

    def __init__(self, check_interval: float = 30.0):
        """
        初始化故障检测器

        Args:
            check_interval: 检查间隔（秒）
        """
        self.logger = get_logger(self.__class__.__name__)
        self.check_interval = check_interval

        # 组件健康状态
        self._component_health: Dict[ComponentType, ComponentHealth] = {}

        # 故障回调函数
        self._fault_callbacks: Dict[ComponentType, List[Callable]] = {}

        # 恢复回调函数
        self._recovery_callbacks: Dict[ComponentType, List[Callable]] = {}

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False

        # 故障历史
        self._fault_history: List[Dict[str, Any]] = []

        # 系统信息缓存
        self._system_info_cache = {}
        self._cache_update_time = 0

        self.logger.info("故障检测器初始化完成")

    def register_fault_callback(
        self, component_type: ComponentType, callback: Callable[[ComponentHealth], None]
    ):
        """注册故障回调函数"""
        if component_type not in self._fault_callbacks:
            self._fault_callbacks[component_type] = []
        self._fault_callbacks[component_type].append(callback)
        self.logger.debug(f"注册故障回调: {component_type.value}")

    def register_recovery_callback(
        self, component_type: ComponentType, callback: Callable[[ComponentHealth], None]
    ):
        """注册恢复回调函数"""
        if component_type not in self._recovery_callbacks:
            self._recovery_callbacks[component_type] = []
        self._recovery_callbacks[component_type].append(callback)
        self.logger.debug(f"注册恢复回调: {component_type.value}")

    async def start_monitoring(self):
        """开始监控"""
        if self._running:
            self.logger.warning("故障检测器已在运行")
            return

        self._running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("故障检测器开始监控")

    async def stop_monitoring(self):
        """停止监控"""
        self._running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        self.logger.info("故障检测器停止监控")

    async def _monitoring_loop(self):
        """监控循环"""
        while self._running:
            try:
                await self._check_all_components()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                await asyncio.sleep(5)  # 出错后短暂等待

    async def _check_all_components(self):
        """检查所有组件"""
        check_tasks = [
            self._check_event_loop(),
            self._check_memory(),
            self._check_cpu(),
            self._check_disk(),
            self._check_task_queue(),
        ]

        await asyncio.gather(*check_tasks, return_exceptions=True)

    async def _check_event_loop(self):
        """检查事件循环健康状态"""
        try:
            start_time = time.time()
            await asyncio.sleep(0)  # 让出控制权测试响应性
            response_time = time.time() - start_time

            # 创建健康指标
            metrics = {
                "response_time": HealthMetric(
                    name="response_time",
                    value=response_time * 1000,  # 转换为毫秒
                    threshold_warning=10.0,
                    threshold_critical=50.0,
                    unit="ms",
                )
            }

            # 判断整体状态
            status = HealthStatus.HEALTHY
            for metric in metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                    break
                elif metric.status == HealthStatus.WARNING:
                    status = HealthStatus.WARNING

            await self._update_component_health(
                ComponentType.EVENT_LOOP, status, metrics
            )

        except Exception as e:
            await self._update_component_health(
                ComponentType.EVENT_LOOP, HealthStatus.CRITICAL, {}, f"事件循环检查失败: {e}"
            )

    async def _check_memory(self):
        """检查内存使用情况"""
        try:
            # 更新系统信息缓存
            await self._update_system_info_cache()

            memory_info = self._system_info_cache.get("memory", {})

            metrics = {
                "usage_percent": HealthMetric(
                    name="usage_percent",
                    value=memory_info.get("percent", 0),
                    threshold_warning=80.0,
                    threshold_critical=95.0,
                    unit="%",
                ),
                "available_gb": HealthMetric(
                    name="available_gb",
                    value=memory_info.get("available", 0) / (1024**3),
                    threshold_warning=1.0,  # 小于1GB警告
                    threshold_critical=0.5,  # 小于500MB严重
                    unit="GB",
                ),
            }

            # 判断整体状态
            status = HealthStatus.HEALTHY
            for metric in metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                    break
                elif metric.status == HealthStatus.WARNING:
                    status = HealthStatus.WARNING

            await self._update_component_health(ComponentType.MEMORY, status, metrics)

        except Exception as e:
            await self._update_component_health(
                ComponentType.MEMORY, HealthStatus.CRITICAL, {}, f"内存检查失败: {e}"
            )

    async def _check_cpu(self):
        """检查CPU使用情况"""
        try:
            await self._update_system_info_cache()

            cpu_percent = self._system_info_cache.get("cpu_percent", 0)

            metrics = {
                "usage_percent": HealthMetric(
                    name="usage_percent",
                    value=cpu_percent,
                    threshold_warning=80.0,
                    threshold_critical=95.0,
                    unit="%",
                )
            }

            status = HealthStatus.HEALTHY
            if cpu_percent >= 95.0:
                status = HealthStatus.CRITICAL
            elif cpu_percent >= 80.0:
                status = HealthStatus.WARNING

            await self._update_component_health(ComponentType.CPU, status, metrics)

        except Exception as e:
            await self._update_component_health(
                ComponentType.CPU, HealthStatus.CRITICAL, {}, f"CPU检查失败: {e}"
            )

    async def _check_disk(self):
        """检查磁盘使用情况"""
        try:
            await self._update_system_info_cache()

            disk_info = self._system_info_cache.get("disk", {})

            metrics = {
                "usage_percent": HealthMetric(
                    name="usage_percent",
                    value=disk_info.get("percent", 0),
                    threshold_warning=85.0,
                    threshold_critical=95.0,
                    unit="%",
                ),
                "free_gb": HealthMetric(
                    name="free_gb",
                    value=disk_info.get("free", 0) / (1024**3),
                    threshold_warning=5.0,  # 小于5GB警告
                    threshold_critical=1.0,  # 小于1GB严重
                    unit="GB",
                ),
            }

            status = HealthStatus.HEALTHY
            for metric in metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                    break
                elif metric.status == HealthStatus.WARNING:
                    status = HealthStatus.WARNING

            await self._update_component_health(ComponentType.DISK, status, metrics)

        except Exception as e:
            await self._update_component_health(
                ComponentType.DISK, HealthStatus.CRITICAL, {}, f"磁盘检查失败: {e}"
            )

    async def _check_task_queue(self):
        """检查任务队列状态"""
        try:
            # 获取当前事件循环中的任务数量
            loop = asyncio.get_running_loop()
            all_tasks = asyncio.all_tasks(loop)
            pending_tasks = [t for t in all_tasks if not t.done()]

            metrics = {
                "pending_tasks": HealthMetric(
                    name="pending_tasks",
                    value=len(pending_tasks),
                    threshold_warning=100,
                    threshold_critical=500,
                    unit="tasks",
                ),
                "total_tasks": HealthMetric(
                    name="total_tasks",
                    value=len(all_tasks),
                    threshold_warning=200,
                    threshold_critical=1000,
                    unit="tasks",
                ),
            }

            status = HealthStatus.HEALTHY
            for metric in metrics.values():
                if metric.status == HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                    break
                elif metric.status == HealthStatus.WARNING:
                    status = HealthStatus.WARNING

            await self._update_component_health(
                ComponentType.TASK_QUEUE, status, metrics
            )

        except Exception as e:
            await self._update_component_health(
                ComponentType.TASK_QUEUE, HealthStatus.CRITICAL, {}, f"任务队列检查失败: {e}"
            )

    async def _update_system_info_cache(self):
        """更新系统信息缓存"""
        current_time = time.time()
        if current_time - self._cache_update_time < 5.0:  # 5秒缓存
            return

        try:
            # 在线程池中执行系统信息收集
            loop = asyncio.get_running_loop()

            def collect_system_info():
                return {
                    "memory": psutil.virtual_memory()._asdict(),
                    "cpu_percent": psutil.cpu_percent(interval=0.1),
                    "disk": psutil.disk_usage("/")._asdict(),
                }

            self._system_info_cache = await loop.run_in_executor(
                None, collect_system_info
            )
            self._cache_update_time = current_time

        except Exception as e:
            self.logger.error(f"更新系统信息缓存失败: {e}")

    async def _update_component_health(
        self,
        component_type: ComponentType,
        status: HealthStatus,
        metrics: Dict[str, HealthMetric],
        error_message: Optional[str] = None,
    ):
        """更新组件健康状态"""
        previous_health = self._component_health.get(component_type)

        # 创建新的健康状态
        new_health = ComponentHealth(
            component_type=component_type,
            status=status,
            metrics=metrics,
            error_message=error_message,
        )

        self._component_health[component_type] = new_health

        # 检查状态变化
        if previous_health:
            if previous_health.status != status:
                await self._handle_status_change(
                    component_type, previous_health.status, status, new_health
                )
        else:
            # 首次检查
            if status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                await self._handle_status_change(
                    component_type, HealthStatus.HEALTHY, status, new_health
                )

    async def _handle_status_change(
        self,
        component_type: ComponentType,
        old_status: HealthStatus,
        new_status: HealthStatus,
        health: ComponentHealth,
    ):
        """处理状态变化"""
        self.logger.info(
            f"组件状态变化: {component_type.value} {old_status.value} -> {new_status.value}"
        )

        # 记录故障历史
        self._fault_history.append(
            {
                "component_type": component_type.value,
                "old_status": old_status.value,
                "new_status": new_status.value,
                "timestamp": time.time(),
                "error_message": health.error_message,
                "metrics": {
                    name: metric.value for name, metric in health.metrics.items()
                },
            }
        )

        # 限制历史记录数量
        if len(self._fault_history) > 1000:
            self._fault_history = self._fault_history[-500:]

        # 触发相应的回调
        if new_status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
            # 故障回调
            callbacks = self._fault_callbacks.get(component_type, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(health)
                    else:
                        callback(health)
                except Exception as e:
                    self.logger.error(f"故障回调执行失败: {e}")

        elif (
            old_status in [HealthStatus.WARNING, HealthStatus.CRITICAL]
            and new_status == HealthStatus.HEALTHY
        ):
            # 恢复回调
            callbacks = self._recovery_callbacks.get(component_type, [])
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(health)
                    else:
                        callback(health)
                except Exception as e:
                    self.logger.error(f"恢复回调执行失败: {e}")

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统整体健康状态"""
        overall_status = HealthStatus.HEALTHY
        component_statuses = {}

        for component_type, health in self._component_health.items():
            component_statuses[component_type.value] = health.to_dict()

            # 确定整体状态
            if health.status == HealthStatus.CRITICAL:
                overall_status = HealthStatus.CRITICAL
            elif (
                health.status == HealthStatus.WARNING
                and overall_status != HealthStatus.CRITICAL
            ):
                overall_status = HealthStatus.WARNING

        return {
            "overall_status": overall_status.value,
            "components": component_statuses,
            "last_check": time.time(),
            "fault_count": len(
                [
                    h
                    for h in self._component_health.values()
                    if h.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]
                ]
            ),
        }

    def get_fault_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取故障历史"""
        return self._fault_history[-limit:]


# 全局故障检测器实例
_fault_detector = None


def get_fault_detector() -> FaultDetector:
    """获取全局故障检测器实例"""
    global _fault_detector
    if _fault_detector is None:
        _fault_detector = FaultDetector()
    return _fault_detector
