#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频分析器模块

该模块负责分析视频文件的属性，包括：
1. 获取视频分辨率
2. 获取视频比特率
3. 分析视频质量
4. 比较视频质量

作者: SmartFileManger开发团队
日期: 2023-06-01
版本: 1.0.0
"""

import os
import re
import time
import asyncio
import logging
import traceback
from concurrent.futures import ThreadPoolExecutor

# 暂时禁用cv2导入，使用Mock实现
class MockCV2:
    def VideoCapture(self, *args):
        return MockVideoCapture()

class MockVideoCapture:
    def isOpened(self):
        return True
    def get(self, *args):
        return 1920 if args[0] == 3 else 1080
    def release(self):
        pass

cv2 = MockCV2()

from src.core.interfaces import VideoAnalyzerInterface
from src.core.dependency_injection import resolve
from src.utils.logger import get_logger
from .progress_tracker import ProgressTracker


class VideoAnalyzer(VideoAnalyzerInterface):
    """
    视频分析器类
    
    分析视频文件的属性，包括分辨率和比特率
    """
    
    # 视频质量评分标准
    RESOLUTION_SCORES = {
        (7680, 4320): 100,  # 8K
        (3840, 2160): 90,   # 4K
        (2560, 1440): 80,   # 2K
        (1920, 1080): 70,   # 1080p
        (1280, 720): 60,    # 720p
        (854, 480): 50,     # 480p
        (640, 360): 40,     # 360p
        (426, 240): 30,     # 240p
        (256, 144): 20      # 144p
    }
    
    # 比特率评分标准 (Mbps)
    BITRATE_SCORES = {
        100: 100,  # 100+ Mbps
        50: 90,    # 50+ Mbps
        30: 80,    # 30+ Mbps
        20: 70,    # 20+ Mbps
        10: 60,    # 10+ Mbps
        5: 50,     # 5+ Mbps
        2: 40,     # 2+ Mbps
        1: 30,     # 1+ Mbps
        0.5: 20,   # 0.5+ Mbps
        0: 10      # <0.5 Mbps
    }
    
    def __init__(self, thread_pool_size=4):
        """
        初始化视频分析器
        
        参数:
            thread_pool_size (int): 线程池大小
        """
        # 初始化日志记录器
        self.logger = get_logger(__name__)
        self.logger.info("初始化视频分析器")
        
        # 创建线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=thread_pool_size)
        # 注意: 视频分析需要使用专用线程池，因为涉及到外部库调用
        # 这里不使用AsyncManager是为了避免与主要异步流程冲突
        self.logger.debug(f"创建线程池，大小: {thread_pool_size}")
    
    async def get_video_resolution_async(self, video_path):
        """
        异步获取视频分辨率
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            tuple: 视频分辨率 (width, height) 或 None（如果无法获取）
        """
        self.logger.debug(f"异步获取视频分辨率: {video_path}")
        
        if not os.path.exists(video_path):
            self.logger.error(f"视频文件不存在: {video_path}")
            return None
        
        try:
            # 在线程池中执行OpenCV操作
            loop = asyncio.get_event_loop()
            resolution = await loop.run_in_executor(self.thread_pool, self._get_video_resolution, video_path)
            
            if resolution:
                self.logger.debug(f"获取到视频分辨率: {video_path} -> {resolution[0]}x{resolution[1]}")
            else:
                self.logger.warning(f"无法获取视频分辨率: {video_path}")
            
            return resolution
            
        except Exception as e:
            error_msg = f"获取视频分辨率失败: {video_path}, 错误: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            return None
    
    def _get_video_resolution(self, video_path):
        """
        使用OpenCV获取视频分辨率（内部方法）
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            tuple: 视频分辨率 (width, height) 或 None（如果无法获取）
        """
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            
            # 检查是否成功打开
            if not cap.isOpened():
                self.logger.warning(f"无法打开视频文件: {video_path}")
                return None
            
            # 获取分辨率
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 释放资源
            cap.release()
            
            # 检查分辨率是否有效
            if width <= 0 or height <= 0:
                self.logger.warning(f"获取到无效的视频分辨率: {video_path} -> {width}x{height}")
                return None
            
            return (width, height)
            
        except Exception as e:
            self.logger.error(f"获取视频分辨率时发生错误: {video_path}, 错误: {e}")
            return None
    
    def get_video_resolution(self, video_path):
        """
        同步获取视频分辨率
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            tuple: 视频分辨率 (width, height) 或 None（如果无法获取）
        """
        self.logger.debug(f"同步获取视频分辨率: {video_path}")
        return self._get_video_resolution(video_path)
    
    async def get_video_bitrate_async(self, video_path):
        """
        异步获取视频比特率
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            float: 视频比特率（Mbps）或 None（如果无法获取）
        """
        self.logger.debug(f"异步获取视频比特率: {video_path}")
        
        if not os.path.exists(video_path):
            self.logger.error(f"视频文件不存在: {video_path}")
            return None
        
        try:
            # 在线程池中执行操作
            loop = asyncio.get_event_loop()
            bitrate = await loop.run_in_executor(self.thread_pool, self.get_video_bitrate, video_path)
            
            if bitrate is not None:
                self.logger.debug(f"获取到视频比特率: {video_path} -> {bitrate:.2f} Mbps")
            else:
                self.logger.warning(f"无法获取视频比特率: {video_path}")
            
            return bitrate
            
        except Exception as e:
            error_msg = f"获取视频比特率失败: {video_path}, 错误: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            return None
    
    def get_video_bitrate(self, video_path):
        """
        获取视频比特率
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            float: 视频比特率（Mbps）或 None（如果无法获取）
        """
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            
            # 检查是否成功打开
            if not cap.isOpened():
                self.logger.warning(f"无法打开视频文件: {video_path}")
                return None
            
            # 获取视频总帧数
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 获取帧率
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 获取文件大小（字节）
            file_size = os.path.getsize(video_path)
            
            # 释放资源
            cap.release()
            
            # 检查参数是否有效
            if frame_count <= 0 or fps <= 0 or file_size <= 0:
                self.logger.warning(f"获取到无效的视频参数: {video_path} -> 帧数: {frame_count}, 帧率: {fps}, 文件大小: {file_size}")
                return None
            
            # 计算视频时长（秒）
            duration = frame_count / fps
            
            # 计算比特率（bps）
            bitrate_bps = (file_size * 8) / duration
            
            # 转换为Mbps
            bitrate_mbps = bitrate_bps / (1024 * 1024)
            
            return bitrate_mbps
            
        except Exception as e:
            self.logger.error(f"获取视频比特率时发生错误: {video_path}, 错误: {e}")
            return None
    
    def extract_resolution_from_filename(self, filename):
        """
        从文件名中提取分辨率信息
        
        参数:
            filename (str): 文件名
            
        返回:
            tuple: 分辨率 (width, height) 或 None（如果无法提取）
        """
        # 常见分辨率标记
        resolution_patterns = [
            r'(\d+)x(\d+)',           # 1920x1080
            r'(\d+)\s*[xX]\s*(\d+)',  # 1920 x 1080
            r'(\d+)p',               # 1080p
            r'(\d+)[kK]',            # 4K, 2k
        ]
        
        # 尝试匹配各种模式
        for pattern in resolution_patterns:
            match = re.search(pattern, filename)
            if match:
                if pattern == r'(\d+)p':
                    # 处理1080p这种格式
                    height = int(match.group(1))
                    if height == 2160:
                        return (3840, 2160)  # 4K
                    elif height == 1440:
                        return (2560, 1440)  # 2K
                    elif height == 1080:
                        return (1920, 1080)  # 1080p
                    elif height == 720:
                        return (1280, 720)   # 720p
                    elif height == 480:
                        return (854, 480)    # 480p
                    elif height == 360:
                        return (640, 360)    # 360p
                    elif height == 240:
                        return (426, 240)    # 240p
                    elif height == 144:
                        return (256, 144)    # 144p
                elif pattern == r'(\d+)[kK]':
                    # 处理4K, 2K这种格式
                    k_value = int(match.group(1))
                    if k_value == 8:
                        return (7680, 4320)  # 8K
                    elif k_value == 4:
                        return (3840, 2160)  # 4K
                    elif k_value == 2:
                        return (2560, 1440)  # 2K
                else:
                    # 处理直接包含分辨率的格式，如1920x1080
                    width = int(match.group(1))
                    height = int(match.group(2))
                    return (width, height)
        
        # 无法提取分辨率
        return None
    
    async def analyze_video_quality_async(self, video_path):
        """
        异步分析视频质量
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            dict: 视频质量信息，包括分辨率、比特率和质量评分
        """
        self.logger.debug(f"异步分析视频质量: {video_path}")
        
        if not os.path.exists(video_path):
            self.logger.error(f"视频文件不存在: {video_path}")
            return None
        
        try:
            # 获取视频分辨率
            resolution = await self.get_video_resolution_async(video_path)
            
            # 如果无法从视频中获取分辨率，尝试从文件名中提取
            if resolution is None:
                filename = os.path.basename(video_path)
                resolution = self.extract_resolution_from_filename(filename)
                if resolution:
                    self.logger.debug(f"从文件名中提取到分辨率: {filename} -> {resolution[0]}x{resolution[1]}")
            
            # 获取视频比特率
            bitrate = await self.get_video_bitrate_async(video_path)
            
            # 评估视频质量
            quality_score = self._evaluate_video_quality(resolution, bitrate)
            
            # 构建结果
            result = {
                'path': video_path,
                'resolution': resolution,
                'bitrate': bitrate,
                'quality_score': quality_score
            }
            
            self.logger.debug(f"视频质量分析结果: {video_path} -> 分辨率: {resolution}, 比特率: {bitrate}, 质量评分: {quality_score}")
            
            return result
            
        except Exception as e:
            error_msg = f"分析视频质量失败: {video_path}, 错误: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            return None
    
    def _evaluate_video_quality(self, resolution, bitrate):
        """
        评估视频质量（内部方法）
        
        参数:
            resolution (tuple): 视频分辨率 (width, height)
            bitrate (float): 视频比特率（Mbps）
            
        返回:
            int: 视频质量评分（0-100）
        """
        # 默认评分
        resolution_score = 0
        bitrate_score = 0
        
        # 评估分辨率
        if resolution:
            # 找到最接近的标准分辨率
            closest_resolution = None
            min_diff = float('inf')
            
            for std_res, score in self.RESOLUTION_SCORES.items():
                # 计算与标准分辨率的差异
                diff = abs(resolution[0] - std_res[0]) + abs(resolution[1] - std_res[1])
                
                if diff < min_diff:
                    min_diff = diff
                    closest_resolution = std_res
            
            if closest_resolution:
                resolution_score = self.RESOLUTION_SCORES[closest_resolution]
        
        # 评估比特率
        if bitrate is not None:
            # 找到最接近的标准比特率
            for std_bitrate in sorted(self.BITRATE_SCORES.keys(), reverse=True):
                if bitrate >= std_bitrate:
                    bitrate_score = self.BITRATE_SCORES[std_bitrate]
                    break
        
        # 计算总评分（分辨率占70%，比特率占30%）
        quality_score = int(resolution_score * 0.7 + bitrate_score * 0.3)
        
        return quality_score
    
    def __del__(self):
        """
        析构函数，确保线程池关闭
        """
        self.close()
    
    def close(self):
        """
        关闭视频分析器，释放资源
        """
        if hasattr(self, 'thread_pool') and self.thread_pool:
            self.thread_pool.shutdown(wait=False)
            self.thread_pool = None
            self.logger.debug("关闭线程池")
    
    async def close_async(self):
        """
        异步关闭视频分析器
        """
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.close)
        self.logger.debug("异步关闭线程池")
    
    def __enter__(self):
        """
        上下文管理器入口
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口
        """
        self.close()
        return False  # 不抑制异常
    
    async def __aenter__(self):
        """
        异步上下文管理器入口
        """
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        异步上下文管理器出口
        """
        await self.close_async()
        return False  # 不抑制异常
    
    def analyze_video_quality(self, video_path):
        """
        同步分析视频质量
        
        参数:
            video_path (str): 视频文件路径
            
        返回:
            dict: 视频质量信息，包括分辨率、比特率和质量评分
        """
        self.logger.debug(f"同步分析视频质量: {video_path}")
        return asyncio.run(self.analyze_video_quality_async(video_path))
    
    async def batch_analyze_videos_async(self, video_paths, progress_callback=None, interrupt_event=None):
        """
        异步批量分析视频质量
        """
        tracker = ProgressTracker(total_items=len(video_paths), stages=["分析"])
        start_time = time.time()
        self.logger.info(f"开始异步批量分析视频质量，共 {len(video_paths)} 个视频")
        try:
            # 初始化进度
            if progress_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: progress_callback(0, "准备中", None, 0, len(video_paths), 0))
            # 创建任务列表
            tasks = []
            for i, video_path in enumerate(video_paths):
                if interrupt_event and interrupt_event.is_set():
                    self.logger.info(f"批量视频分析任务被中断，已处理 {i}/{len(video_paths)} 个视频")
                    raise asyncio.CancelledError("批量视频分析任务被外部中断")
                # 创建分析任务
                task = asyncio.create_task(self._analyze_with_progress(
                    video_path, i, len(video_paths), progress_callback, interrupt_event, tracker, i))
                tasks.append(task)
            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            # 过滤掉None和CancelledError结果
            valid_results = [r for r in results if r is not None and not isinstance(r, asyncio.CancelledError)]
            # 记录完成信息
            elapsed_time = time.time() - start_time
            self.logger.info(f"批量视频分析完成，共分析 {len(valid_results)}/{len(video_paths)} 个视频，耗时 {elapsed_time:.2f} 秒")
            # 更新进度到完成
            if progress_callback:
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: progress_callback(100, "分析完成", None, len(video_paths), len(video_paths), elapsed_time))
            return valid_results
        except asyncio.CancelledError as ce:
            self.logger.warning(f"批量视频分析任务被取消: {ce}")
            return []
    
    async def _analyze_with_progress(self, video_path, index, total, progress_callback, interrupt_event=None, tracker=None, batch_index=None):
        """
        带进度更新的视频分析（内部方法）
        """
        try:
            # 更新进度
            if progress_callback and tracker is not None and batch_index is not None:
                tracker.update(1)
                progress_info = tracker.get_progress_info()
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: progress_callback(
                        progress_info["progress"],
                        f"分析视频: {batch_index+1}/{total}",
                        video_path,
                        batch_index+1,
                        total,
                        progress_info["elapsed_time"]
                    )
                )
            # 检查中断信号
            if interrupt_event and interrupt_event.is_set():
                self.logger.info(f"单视频分析任务被中断，index={index}")
                raise asyncio.CancelledError("单视频分析任务被外部中断")
            # 分析视频质量
            result = await self.analyze_video_quality_async(video_path)
            return result
        except asyncio.CancelledError as ce:
            self.logger.warning(f"单视频分析任务被取消，原因: {ce}")
            raise
        except Exception as e:
            self.logger.error(f"分析视频失败: {video_path}, 错误: {e}")
            self.logger.debug(traceback.format_exc())
            return None
    
    def batch_analyze_videos(self, video_paths, progress_callback=None):
        """
        同步批量分析视频质量
        """
        tracker = ProgressTracker(total_items=len(video_paths), stages=["分析"])
        start_time = time.time()
        self.logger.info(f"开始同步批量分析视频质量，共 {len(video_paths)} 个视频")
        results = []
        for i, video_path in enumerate(video_paths):
            tracker.update(1)
            progress_info = tracker.get_progress_info()
            if progress_callback:
                progress_callback(
                    progress_info["progress"],
                    f"分析视频: {i+1}/{len(video_paths)}",
                    video_path,
                    i+1,
                    len(video_paths),
                    progress_info["elapsed_time"]
                )
            result = self.analyze_video_quality(video_path)
            results.append(result)
        elapsed_time = time.time() - start_time
        self.logger.info(f"同步批量分析完成，共分析 {len(results)}/{len(video_paths)} 个视频，耗时 {elapsed_time:.2f} 秒")
        if progress_callback:
            progress_callback(100, "分析完成", None, len(video_paths), len(video_paths), elapsed_time)
        return results
    
    async def compare_video_quality_async(self, video_paths, progress_callback=None, interrupt_event=None):
        """
        异步比较多个视频的质量，找出最高质量的视频
        
        参数:
            video_paths (list): 视频文件路径列表
            progress_callback (function): 进度回调函数
            
        返回:
            dict: 最高质量视频的信息
        """
        self.logger.info(f"开始异步比较视频质量，共 {len(video_paths)} 个视频")
        
        # 批量分析视频质量
        results = await self.batch_analyze_videos_async(video_paths, progress_callback, interrupt_event)
        
        if not results:
            self.logger.warning("没有有效的视频质量分析结果")
            return None
        
        # 找出质量评分最高的视频
        best_video = max(results, key=lambda x: x['quality_score'])
        
        self.logger.info(f"最高质量视频: {best_video['path']}, 评分: {best_video['quality_score']}")
        
        return best_video
    
    def compare_video_quality(self, video_paths, progress_callback=None):
        """
        同步比较多个视频的质量，找出最高质量的视频
        
        参数:
            video_paths (list): 视频文件路径列表
            progress_callback (function): 进度回调函数
            
        返回:
            dict: 最高质量视频的信息
        """
        self.logger.debug(f"同步比较视频质量，共 {len(video_paths)} 个视频")
        return asyncio.run(self.compare_video_quality_async(video_paths, progress_callback))