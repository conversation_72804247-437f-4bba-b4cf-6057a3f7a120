#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强日志系统

提供结构化日志、性能跟踪、调试支持和日志分析功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import json
import logging
import os
import sys
import threading
import time
import traceback
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from ..logger import get_logger

logger = get_logger("EnhancedLogging")


class LogLevel(Enum):
    """日志级别枚举"""

    TRACE = 5
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50


class LogCategory(Enum):
    """日志分类枚举"""

    SYSTEM = "system"
    PERFORMANCE = "performance"
    SECURITY = "security"
    BUSINESS = "business"
    DEBUG = "debug"
    AUDIT = "audit"


@dataclass
class LogContext:
    """日志上下文"""

    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    task_id: Optional[str] = None
    module_name: Optional[str] = None
    function_name: Optional[str] = None
    thread_id: Optional[str] = None
    correlation_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in self.__dict__.items() if v is not None}


@dataclass
class PerformanceMetric:
    """性能指标"""

    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    cpu_usage: Optional[float] = None
    custom_metrics: Dict[str, Any] = field(default_factory=dict)

    def finish(self):
        """完成性能测量"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time


class StructuredLogger:
    """
    结构化日志记录器

    提供结构化日志记录、性能跟踪和调试支持
    """

    def __init__(self, name: str):
        """初始化结构化日志记录器"""
        self.name = name
        self.logger = logging.getLogger(name)

        # 上下文存储
        self._context_stack: List[LogContext] = []
        self._thread_local = threading.local()

        # 性能跟踪
        self._performance_metrics: Dict[str, PerformanceMetric] = {}

        # 日志缓冲区
        self._log_buffer: List[Dict[str, Any]] = []
        self._buffer_lock = threading.Lock()

        # 配置
        self._config = {
            "enable_performance_tracking": True,
            "enable_memory_tracking": True,
            "enable_stack_trace": True,
            "buffer_size": 1000,
            "auto_flush_interval": 60,
        }

        # 设置日志格式
        self._setup_formatter()

        # 启动自动刷新
        self._start_auto_flush()

    def _setup_formatter(self):
        """设置日志格式器"""

        class StructuredFormatter(logging.Formatter):
            def format(self, record):
                # 创建结构化日志记录
                log_entry = {
                    "timestamp": time.time(),
                    "level": record.levelname,
                    "logger": record.name,
                    "message": record.getMessage(),
                    "module": record.module,
                    "function": record.funcName,
                    "line": record.lineno,
                    "thread": threading.current_thread().name,
                    "process": os.getpid(),
                }

                # 添加异常信息
                if record.exc_info:
                    log_entry["exception"] = {
                        "type": record.exc_info[0].__name__,
                        "message": str(record.exc_info[1]),
                        "traceback": traceback.format_exception(*record.exc_info),
                    }

                # 添加自定义字段
                if hasattr(record, "extra_fields"):
                    log_entry.update(record.extra_fields)

                return json.dumps(log_entry, ensure_ascii=False)

        formatter = StructuredFormatter()

        # 设置控制台处理器
        if not self.logger.handlers:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            self.logger.setLevel(logging.DEBUG)

    def _start_auto_flush(self):
        """启动自动刷新任务"""

        def auto_flush():
            while True:
                time.sleep(self._config["auto_flush_interval"])
                self.flush_buffer()

        flush_thread = threading.Thread(target=auto_flush, daemon=True)
        flush_thread.start()

    @contextmanager
    def context(self, **kwargs):
        """日志上下文管理器"""
        context = LogContext(**kwargs)
        self._context_stack.append(context)
        try:
            yield context
        finally:
            if self._context_stack:
                self._context_stack.pop()

    def _get_current_context(self) -> Dict[str, Any]:
        """获取当前上下文"""
        if self._context_stack:
            return self._context_stack[-1].to_dict()
        return {}

    def _log_structured(
        self,
        level: LogLevel,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        **kwargs,
    ):
        """记录结构化日志"""
        # 构建日志记录
        log_record = {
            "timestamp": time.time(),
            "level": level.name,
            "category": category.value,
            "message": message,
            "context": self._get_current_context(),
            **kwargs,
        }

        # 添加到缓冲区
        with self._buffer_lock:
            self._log_buffer.append(log_record)

            # 检查缓冲区大小
            if len(self._log_buffer) >= self._config["buffer_size"]:
                self._flush_buffer_unsafe()

        # 同时使用标准日志记录
        extra_fields = {
            "category": category.value,
            "context": self._get_current_context(),
            **kwargs,
        }

        # 创建LogRecord并添加额外字段
        record = self.logger.makeRecord(
            self.logger.name, level.value, "", 0, message, (), None
        )
        record.extra_fields = extra_fields

        self.logger.handle(record)

    def trace(self, message: str, **kwargs):
        """记录跟踪日志"""
        self._log_structured(LogLevel.TRACE, message, **kwargs)

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self._log_structured(LogLevel.DEBUG, message, LogCategory.DEBUG, **kwargs)

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log_structured(LogLevel.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self._log_structured(LogLevel.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self._log_structured(LogLevel.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        self._log_structured(LogLevel.CRITICAL, message, **kwargs)

    def performance(self, message: str, **metrics):
        """记录性能日志"""
        self._log_structured(
            LogLevel.INFO, message, LogCategory.PERFORMANCE, metrics=metrics
        )

    def security(self, message: str, **kwargs):
        """记录安全日志"""
        self._log_structured(LogLevel.WARNING, message, LogCategory.SECURITY, **kwargs)

    def audit(self, message: str, **kwargs):
        """记录审计日志"""
        self._log_structured(LogLevel.INFO, message, LogCategory.AUDIT, **kwargs)

    @contextmanager
    def performance_tracking(self, operation_name: str, **custom_metrics):
        """性能跟踪上下文管理器"""
        if not self._config["enable_performance_tracking"]:
            yield
            return

        metric = PerformanceMetric(
            operation_name=operation_name,
            start_time=time.time(),
            custom_metrics=custom_metrics,
        )

        # 记录内存使用（如果启用）
        if self._config["enable_memory_tracking"]:
            try:
                import psutil

                process = psutil.Process()
                metric.memory_before = process.memory_info().rss / 1024 / 1024  # MB
            except ImportError:
                pass

        try:
            yield metric
        finally:
            metric.finish()

            # 记录结束时的内存使用
            if self._config["enable_memory_tracking"]:
                try:
                    import psutil

                    process = psutil.Process()
                    metric.memory_after = process.memory_info().rss / 1024 / 1024  # MB
                except ImportError:
                    pass

            # 记录性能日志
            self.performance(
                f"操作完成: {operation_name}",
                duration=metric.duration,
                memory_before=metric.memory_before,
                memory_after=metric.memory_after,
                **metric.custom_metrics,
            )

            # 存储指标
            self._performance_metrics[f"{operation_name}_{int(time.time())}"] = metric

    def log_exception(self, exc: Exception, message: str = "异常发生", **kwargs):
        """记录异常日志"""
        exc_info = {
            "type": type(exc).__name__,
            "message": str(exc),
            "traceback": traceback.format_exc()
            if self._config["enable_stack_trace"]
            else None,
        }

        self._log_structured(LogLevel.ERROR, message, exception=exc_info, **kwargs)

    def log_async_operation(
        self, operation_name: str, task_id: str, status: str, **kwargs
    ):
        """记录异步操作日志"""
        with self.context(task_id=task_id):
            self.info(
                f"异步操作: {operation_name}",
                operation=operation_name,
                status=status,
                **kwargs,
            )

    def flush_buffer(self):
        """刷新日志缓冲区"""
        with self._buffer_lock:
            self._flush_buffer_unsafe()

    def _flush_buffer_unsafe(self):
        """不安全的缓冲区刷新（需要在锁内调用）"""
        if not self._log_buffer:
            return

        # 这里可以实现将缓冲区内容写入文件、数据库或发送到日志服务
        # 当前实现只是清空缓冲区
        flushed_count = len(self._log_buffer)
        self._log_buffer.clear()

        # 记录刷新操作
        self.debug(f"日志缓冲区已刷新，处理 {flushed_count} 条记录")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self._performance_metrics:
            return {"total_operations": 0}

        durations = [
            m.duration for m in self._performance_metrics.values() if m.duration
        ]

        return {
            "total_operations": len(self._performance_metrics),
            "avg_duration": sum(durations) / len(durations) if durations else 0,
            "min_duration": min(durations) if durations else 0,
            "max_duration": max(durations) if durations else 0,
            "recent_operations": list(self._performance_metrics.keys())[-10:],
        }

    def configure(self, **config):
        """配置日志记录器"""
        self._config.update(config)


class LogAnalyzer:
    """
    日志分析器

    提供日志分析、统计和报告功能
    """

    def __init__(self):
        """初始化日志分析器"""
        self.logger = get_logger(self.__class__.__name__)
        self._log_entries: List[Dict[str, Any]] = []

    def analyze_logs(self, log_file: str) -> Dict[str, Any]:
        """分析日志文件"""
        try:
            with open(log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        self._log_entries.append(log_entry)
                    except json.JSONDecodeError:
                        continue

            return self._generate_analysis_report()

        except Exception as e:
            self.logger.error(f"日志分析失败: {e}")
            return {}

    def _generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        if not self._log_entries:
            return {"error": "没有有效的日志条目"}

        # 统计信息
        level_counts = {}
        category_counts = {}
        error_patterns = []
        performance_stats = []

        for entry in self._log_entries:
            # 统计日志级别
            level = entry.get("level", "UNKNOWN")
            level_counts[level] = level_counts.get(level, 0) + 1

            # 统计日志分类
            category = entry.get("category", "UNKNOWN")
            category_counts[category] = category_counts.get(category, 0) + 1

            # 收集错误模式
            if level in ["ERROR", "CRITICAL"]:
                error_patterns.append(
                    {
                        "message": entry.get("message", ""),
                        "timestamp": entry.get("timestamp", 0),
                        "exception": entry.get("exception", {}),
                    }
                )

            # 收集性能数据
            if category == "performance":
                metrics = entry.get("metrics", {})
                if "duration" in metrics:
                    performance_stats.append(metrics["duration"])

        # 生成报告
        report = {
            "total_entries": len(self._log_entries),
            "level_distribution": level_counts,
            "category_distribution": category_counts,
            "error_count": len(error_patterns),
            "recent_errors": error_patterns[-10:],  # 最近10个错误
            "performance_summary": {
                "total_operations": len(performance_stats),
                "avg_duration": sum(performance_stats) / len(performance_stats)
                if performance_stats
                else 0,
                "max_duration": max(performance_stats) if performance_stats else 0,
            },
        }

        return report


# 全局日志记录器注册表
_loggers: Dict[str, StructuredLogger] = {}
_logger_lock = threading.Lock()


def get_structured_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器"""
    with _logger_lock:
        if name not in _loggers:
            _loggers[name] = StructuredLogger(name)
        return _loggers[name]


# 装饰器函数
def log_performance(operation_name: str = None):
    """性能日志装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            logger = get_structured_logger(func.__module__)

            with logger.performance_tracking(name):
                return func(*args, **kwargs)

        return wrapper

    return decorator


def log_async_performance(operation_name: str = None):
    """异步性能日志装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            logger = get_structured_logger(func.__module__)

            with logger.performance_tracking(name):
                return await func(*args, **kwargs)

        return wrapper

    return decorator


def log_exceptions(logger_name: str = None):
    """异常日志装饰器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            name = logger_name or func.__module__
            logger = get_structured_logger(name)

            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.log_exception(e, f"函数 {func.__name__} 执行失败")
                raise

        return wrapper

    return decorator


# 调试工具
class DebugHelper:
    """
    调试辅助工具

    提供调试信息收集、状态快照和问题诊断功能
    """

    def __init__(self):
        """初始化调试辅助工具"""
        self.logger = get_structured_logger("DebugHelper")
        self._debug_sessions: Dict[str, Dict[str, Any]] = {}
        self._breakpoints: Dict[str, Callable] = {}

    def start_debug_session(self, session_id: str, context: Dict[str, Any] = None):
        """开始调试会话"""
        self._debug_sessions[session_id] = {
            "start_time": time.time(),
            "context": context or {},
            "snapshots": [],
            "events": [],
        }

        self.logger.debug(f"开始调试会话: {session_id}", session_id=session_id)

    def take_snapshot(self, session_id: str, name: str, data: Dict[str, Any]):
        """拍摄状态快照"""
        if session_id in self._debug_sessions:
            snapshot = {"name": name, "timestamp": time.time(), "data": data}
            self._debug_sessions[session_id]["snapshots"].append(snapshot)

            self.logger.debug(f"状态快照: {name}", session_id=session_id, snapshot=snapshot)

    def log_debug_event(self, session_id: str, event: str, data: Dict[str, Any] = None):
        """记录调试事件"""
        if session_id in self._debug_sessions:
            event_record = {
                "event": event,
                "timestamp": time.time(),
                "data": data or {},
            }
            self._debug_sessions[session_id]["events"].append(event_record)

            self.logger.debug(
                f"调试事件: {event}", session_id=session_id, event_data=event_record
            )

    def end_debug_session(self, session_id: str) -> Dict[str, Any]:
        """结束调试会话并返回报告"""
        if session_id not in self._debug_sessions:
            return {}

        session = self._debug_sessions[session_id]
        session["end_time"] = time.time()
        session["duration"] = session["end_time"] - session["start_time"]

        self.logger.info(
            f"调试会话结束: {session_id}",
            session_id=session_id,
            duration=session["duration"],
            snapshot_count=len(session["snapshots"]),
            event_count=len(session["events"]),
        )

        # 生成调试报告
        report = session.copy()
        del self._debug_sessions[session_id]

        return report

    @contextmanager
    def debug_context(self, session_id: str, **context):
        """调试上下文管理器"""
        self.start_debug_session(session_id, context)
        try:
            yield self
        finally:
            self.end_debug_session(session_id)


# 全局调试辅助工具实例
_debug_helper = None


def get_debug_helper() -> DebugHelper:
    """获取全局调试辅助工具实例"""
    global _debug_helper
    if _debug_helper is None:
        _debug_helper = DebugHelper()
    return _debug_helper
