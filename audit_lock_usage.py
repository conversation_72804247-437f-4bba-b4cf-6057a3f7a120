#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
锁使用审计脚本

审计项目中所有锁的使用情况，识别需要统一的混合锁使用模式

作者: AI助手
日期: 2025-01-30
"""

import os
import re
import ast
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass
from pathlib import Path


@dataclass
class LockUsage:
    """锁使用信息"""
    file_path: str
    line_number: int
    lock_type: str  # 'threading.Lock', 'asyncio.Lock'
    usage_context: str  # 'async_method', 'sync_method', 'init'
    code_snippet: str
    is_problematic: bool = False
    recommendation: str = ""


class LockUsageAuditor:
    """锁使用审计器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.lock_usages: List[LockUsage] = []
        self.problematic_usages: List[LockUsage] = []
    
    def audit_project(self):
        """审计整个项目的锁使用"""
        print("🔍 开始审计项目锁使用情况...")
        
        # 遍历所有Python文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过一些目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', '.venv']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self._audit_file(file_path)
        
        # 分析问题
        self._analyze_problems()
        
        # 生成报告
        self._generate_report()
    
    def _audit_file(self, file_path: str):
        """审计单个文件的锁使用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # 解析AST
            try:
                tree = ast.parse(content)
            except SyntaxError:
                print(f"⚠️ 跳过语法错误文件: {file_path}")
                return
            
            # 查找锁的定义和使用
            self._find_lock_definitions(file_path, tree, lines)
            self._find_lock_usages(file_path, tree, lines)
            
        except Exception as e:
            print(f"⚠️ 处理文件失败: {file_path}, 错误: {e}")
    
    def _find_lock_definitions(self, file_path: str, tree: ast.AST, lines: List[str]):
        """查找锁的定义"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Attribute) and target.attr.endswith('_lock'):
                        # 检查赋值的值
                        if isinstance(node.value, ast.Call):
                            if self._is_threading_lock_call(node.value):
                                self._add_lock_usage(
                                    file_path, node.lineno, 'threading.Lock',
                                    self._get_method_context(tree, node),
                                    lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                                )
                            elif self._is_asyncio_lock_call(node.value):
                                self._add_lock_usage(
                                    file_path, node.lineno, 'asyncio.Lock',
                                    self._get_method_context(tree, node),
                                    lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                                )
    
    def _find_lock_usages(self, file_path: str, tree: ast.AST, lines: List[str]):
        """查找锁的使用"""
        for node in ast.walk(tree):
            # 查找 with self._lock: 语句
            if isinstance(node, ast.With):
                for item in node.items:
                    if isinstance(item.context_expr, ast.Attribute) and item.context_expr.attr.endswith('_lock'):
                        context = self._get_method_context(tree, node)
                        self._add_lock_usage(
                            file_path, node.lineno, 'with_statement',
                            context,
                            lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                        )
            
            # 查找 async with self._lock: 语句
            if isinstance(node, ast.AsyncWith):
                for item in node.items:
                    if isinstance(item.context_expr, ast.Attribute) and item.context_expr.attr.endswith('_lock'):
                        context = self._get_method_context(tree, node)
                        self._add_lock_usage(
                            file_path, node.lineno, 'async_with_statement',
                            context,
                            lines[node.lineno - 1] if node.lineno <= len(lines) else ""
                        )
    
    def _is_threading_lock_call(self, node: ast.Call) -> bool:
        """判断是否是threading.Lock()调用"""
        if isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name) and node.func.value.id == 'threading':
                return node.func.attr in ['Lock', 'RLock']
        elif isinstance(node.func, ast.Name):
            return node.func.id in ['Lock', 'RLock']
        return False
    
    def _is_asyncio_lock_call(self, node: ast.Call) -> bool:
        """判断是否是asyncio.Lock()调用"""
        if isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name) and node.func.value.id == 'asyncio':
                return node.func.attr == 'Lock'
        elif isinstance(node.func, ast.Name):
            return node.func.id == 'Lock'
        return False
    
    def _get_method_context(self, tree: ast.AST, target_node: ast.AST) -> str:
        """获取节点所在的方法上下文"""
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if self._node_in_function(target_node, node):
                    if isinstance(node, ast.AsyncFunctionDef):
                        return f"async_method:{node.name}"
                    else:
                        return f"sync_method:{node.name}"
            elif isinstance(node, ast.ClassDef):
                if self._node_in_class(target_node, node):
                    return f"class_init:{node.name}"
        return "module_level"
    
    def _node_in_function(self, target_node: ast.AST, func_node: ast.FunctionDef) -> bool:
        """判断目标节点是否在函数内"""
        return (hasattr(target_node, 'lineno') and hasattr(func_node, 'lineno') and
                target_node.lineno >= func_node.lineno and
                target_node.lineno <= getattr(func_node, 'end_lineno', func_node.lineno + 100))
    
    def _node_in_class(self, target_node: ast.AST, class_node: ast.ClassDef) -> bool:
        """判断目标节点是否在类内"""
        return (hasattr(target_node, 'lineno') and hasattr(class_node, 'lineno') and
                target_node.lineno >= class_node.lineno and
                target_node.lineno <= getattr(class_node, 'end_lineno', class_node.lineno + 1000))
    
    def _add_lock_usage(self, file_path: str, line_number: int, lock_type: str, 
                       context: str, code_snippet: str):
        """添加锁使用记录"""
        usage = LockUsage(
            file_path=file_path,
            line_number=line_number,
            lock_type=lock_type,
            usage_context=context,
            code_snippet=code_snippet.strip()
        )
        self.lock_usages.append(usage)
    
    def _analyze_problems(self):
        """分析问题锁使用"""
        for usage in self.lock_usages:
            # 检查在异步方法中使用threading.Lock
            if ('async_method' in usage.usage_context and 
                usage.lock_type == 'threading.Lock'):
                usage.is_problematic = True
                usage.recommendation = "在异步方法中应使用asyncio.Lock()而不是threading.Lock()"
                self.problematic_usages.append(usage)
            
            # 检查在同步方法中使用asyncio.Lock
            elif ('sync_method' in usage.usage_context and 
                  usage.lock_type == 'asyncio.Lock'):
                usage.is_problematic = True
                usage.recommendation = "在同步方法中应使用threading.Lock()而不是asyncio.Lock()"
                self.problematic_usages.append(usage)
            
            # 检查with语句和锁类型的匹配
            elif (usage.lock_type == 'with_statement' and 
                  'async_method' in usage.usage_context):
                usage.is_problematic = True
                usage.recommendation = "在异步方法中应使用'async with'而不是'with'"
                self.problematic_usages.append(usage)
            
            elif (usage.lock_type == 'async_with_statement' and 
                  'sync_method' in usage.usage_context):
                usage.is_problematic = True
                usage.recommendation = "在同步方法中应使用'with'而不是'async with'"
                self.problematic_usages.append(usage)
    
    def _generate_report(self):
        """生成审计报告"""
        print("\n" + "="*80)
        print("锁使用审计报告")
        print("="*80)
        
        print(f"总锁使用数: {len(self.lock_usages)}")
        print(f"问题锁使用数: {len(self.problematic_usages)}")
        
        if self.problematic_usages:
            print(f"\n❌ 发现 {len(self.problematic_usages)} 个问题锁使用:")
            print("-"*80)
            
            for usage in self.problematic_usages:
                rel_path = os.path.relpath(usage.file_path, self.project_root)
                print(f"📁 {rel_path}:{usage.line_number}")
                print(f"   锁类型: {usage.lock_type}")
                print(f"   上下文: {usage.usage_context}")
                print(f"   代码: {usage.code_snippet}")
                print(f"   建议: {usage.recommendation}")
                print()
        else:
            print("\n✅ 未发现问题锁使用")
        
        # 统计信息
        print("\n📊 锁使用统计:")
        print("-"*40)
        
        lock_type_count = {}
        context_count = {}
        
        for usage in self.lock_usages:
            lock_type_count[usage.lock_type] = lock_type_count.get(usage.lock_type, 0) + 1
            context_count[usage.usage_context] = context_count.get(usage.usage_context, 0) + 1
        
        print("按锁类型:")
        for lock_type, count in lock_type_count.items():
            print(f"  {lock_type}: {count}")
        
        print("\n按上下文:")
        for context, count in context_count.items():
            print(f"  {context}: {count}")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    project_root = "src"  # 项目根目录
    
    auditor = LockUsageAuditor(project_root)
    auditor.audit_project()


if __name__ == "__main__":
    main()
