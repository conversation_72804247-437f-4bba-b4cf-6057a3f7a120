#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务管理器模块

该模块专门处理异步任务的中止，解决asyncio.run()创建新事件循环导致的中止信号无法传递问题。

作者: AI助手
日期: 2024-01-01
版本: 2.0.0 - 使用统一类型系统
"""

import asyncio
import threading
import time
import uuid
from typing import Callable, Any, Optional, Dict, Coroutine

from .logger import get_logger
from src.utils.format_utils import _normalize_path
from src.utils.unified_types import (
    TaskStatus, TaskType, UnifiedTaskResult, ProgressInfo,
    ProgressCallback, TaskCompleteCallback, create_task_result
)

# 创建日志记录器
logger = get_logger("AsyncTaskManager")

# 兼容性：保留旧的AsyncTaskResult类型别名
AsyncTaskResult = UnifiedTaskResult


class AsyncTaskManager:
    """
    异步任务管理器
    
    专门处理异步任务的中止，避免asyncio.run()创建新事件循环的问题
    """
    
    def __init__(self):
        """初始化异步任务管理器"""
        self.tasks: Dict[str, UnifiedTaskResult] = {}
        self.lock = asyncio.Lock() # 改为异步锁
        self.interrupt_events: Dict[str, asyncio.Event] = {} # 改为异步事件

        logger.info("异步任务管理器初始化完成")

    def _generate_task_id(self) -> str:
        """生成唯一的任务ID"""
        import uuid
        return f"async_task_{uuid.uuid4().hex[:8]}"

    def _format_duration(self, duration: float) -> str:
        """格式化耗时显示"""
        if duration < 1:
            return f"{duration*1000:.0f}ms"
        elif duration < 60:
            return f"{duration:.2f}s"
        elif duration < 3600:
            minutes = int(duration // 60)
            seconds = duration % 60
            return f"{minutes}m{seconds:.1f}s"
        else:
            hours = int(duration // 3600)
            minutes = int((duration % 3600) // 60)
            seconds = duration % 60
            return f"{hours}h{minutes}m{seconds:.0f}s"

    async def submit_async_task(self, task_id: Optional[str], coro: Coroutine,
                               task_type: TaskType = TaskType.GENERAL,
                               interrupt_event: Optional[asyncio.Event] = None) -> str:
        """
        提交异步任务

        参数:
            task_id: 任务ID，如果为None则自动生成
            coro: 协程对象
            task_type: 任务类型
            interrupt_event: 可选的外部中断事件

        返回:
            任务ID
        """
        async with self.lock:
            # 如果task_id为None，自动生成一个
            if task_id is None:
                task_id = self._generate_task_id()

            if task_id in self.tasks:
                logger.warning(f"任务ID已存在: {task_id}")
                return task_id

            # 创建任务结果
            task_result = create_task_result(
                task_id=task_id,
                task_type=task_type,
                status=TaskStatus.PENDING
            )
            task_result.start_time = time.time()
            self.tasks[task_id] = task_result
            
            # 创建或使用外部中断事件
            self.interrupt_events[task_id] = interrupt_event or asyncio.Event()
            
            logger.info(f"提交异步任务: {task_id}")
        
        # 在事件循环中创建任务
        async def task_wrapper():
            """任务包装器"""
            try:
                # 更新状态为运行中
                async with self.lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.RUNNING
                
                # 获取中断事件
                _interrupt_event = self.interrupt_events.get(task_id)

                # 检查是否被中断
                if _interrupt_event and _interrupt_event.is_set():
                    raise asyncio.CancelledError("任务被用户中止")
                
                # 执行协程
                result = await coro
                
                # 再次检查中断
                if _interrupt_event and _interrupt_event.is_set():
                    raise asyncio.CancelledError("任务被用户中止")
                
                # 更新状态为完成
                async with self.lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.COMPLETED
                        self.tasks[task_id].result = result
                        self.tasks[task_id].end_time = time.time()

                        # 计算耗时
                        duration = self.tasks[task_id].end_time - self.tasks[task_id].start_time
                        duration_str = self._format_duration(duration)

                        logger.info(f"异步任务完成: {task_id}，耗时: {duration_str}")
                    else:
                        logger.info(f"异步任务完成: {task_id}")

                return result
                
            except asyncio.CancelledError as e:
                # 任务被取消
                async with self.lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.CANCELLED
                        self.tasks[task_id].error = Exception(str(e))
                        self.tasks[task_id].end_time = time.time()

                        # 计算耗时
                        duration = self.tasks[task_id].end_time - self.tasks[task_id].start_time
                        duration_str = self._format_duration(duration)

                        logger.info(f"异步任务被中断: {task_id}，耗时: {duration_str}")
                    else:
                        logger.info(f"异步任务被中断: {task_id}")
                raise
                
            except Exception as e:
                # 任务执行失败
                async with self.lock:
                    if task_id in self.tasks:
                        self.tasks[task_id].status = TaskStatus.FAILED
                        self.tasks[task_id].error = e
                        self.tasks[task_id].end_time = time.time()

                        # 计算耗时
                        duration = self.tasks[task_id].end_time - self.tasks[task_id].start_time
                        duration_str = self._format_duration(duration)

                        logger.error(f"异步任务失败: {task_id}，耗时: {duration_str}，错误: {e}")
                    else:
                        logger.error(f"异步任务失败: {task_id}，错误: {e}")
                raise
        
        # 在当前事件循环中创建任务
        loop = asyncio.get_running_loop()
        future = loop.create_task(task_wrapper())
        
        # 保存future引用
        async with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id].metadata = {"future": future}
        
        return task_id
    
    async def interrupt_task(self, task_id: str) -> bool:
        """
        中断异步任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功发送中断信号
        """
        async with self.lock:
            if task_id not in self.interrupt_events:
                logger.warning(f"任务的中断事件不存在: {task_id}")
                return False
            
            # 设置中断标志
            self.interrupt_events[task_id].set()
            logger.info(f"设置异步任务中断标志: {task_id}")
            return True
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息
        """
        async with self.lock:
            if task_id not in self.tasks:
                return None
            
            task_result = self.tasks[task_id]
            return {
                "status": task_result.status.name.lower(),
                "execution_time": task_result.execution_time,
                "start_time": task_result.start_time,
                "end_time": task_result.end_time,
                "result": task_result.result,
                "error": str(task_result.error) if task_result.error else None
            }
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Optional[UnifiedTaskResult]:
        """
        等待任务完成
        
        参数:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        返回:
            任务结果
        """
        start_time = time.time()
        
        while True:
            # 检查超时
            if timeout is not None and time.time() - start_time > timeout:
                logger.warning(f"等待任务超时: {task_id}")
                return None
            
            # 获取任务状态
            async with self.lock:
                if task_id not in self.tasks:
                    logger.warning(f"任务不存在: {task_id}")
                    return None
                
                task_result = self.tasks[task_id]
                
                # 检查是否完成
                if task_result.status in (TaskStatus.COMPLETED, TaskStatus.CANCELLED, TaskStatus.FAILED):
                    return task_result
            
            # 短暂等待
            await asyncio.sleep(0.1)
    
    def shutdown(self):
        """关闭异步任务管理器"""
        logger.info("关闭异步任务管理器")
        # TODO: 实现任务取消逻辑
        logger.info("异步任务管理器已关闭")


# 全局异步任务管理器实例
_async_task_manager = None
_async_task_manager_lock = threading.Lock()


def get_async_task_manager() -> AsyncTaskManager:
    """获取全局异步任务管理器实例"""
    global _async_task_manager
    with _async_task_manager_lock:
        if _async_task_manager is None:
            _async_task_manager = AsyncTaskManager()
        return _async_task_manager


def submit_async_task(task_id: str, coro: Coroutine) -> str:
    """提交异步任务的便捷函数"""
    manager = get_async_task_manager()
    return manager.submit_async_task(task_id, coro)


def cancel_async_task(task_id: str) -> bool:
    """取消异步任务的便捷函数"""
    manager = get_async_task_manager()
    return manager.interrupt_task(task_id) # Changed from cancel_async_task to interrupt_task


def get_async_task_status(task_id: str) -> Optional[Dict[str, Any]]:
    """获取异步任务状态的便捷函数"""
    manager = get_async_task_manager()
    return manager.get_task_status(task_id) 