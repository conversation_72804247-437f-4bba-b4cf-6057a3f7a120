#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存优化器

提供内存使用优化、对象池管理和内存泄漏检测功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import gc
import os
import sys
import threading
import time
import weakref
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, Generic, List, Optional, Type, TypeVar

import psutil

from ..logger import get_logger

logger = get_logger("MemoryOptimizer")

T = TypeVar("T")


class MemoryStrategy(Enum):
    """内存优化策略枚举"""

    OBJECT_POOLING = "object_pooling"
    LAZY_LOADING = "lazy_loading"
    WEAK_REFERENCES = "weak_references"
    MEMORY_MAPPING = "memory_mapping"
    COMPRESSION = "compression"
    BATCH_PROCESSING = "batch_processing"


@dataclass
class MemoryMetrics:
    """内存指标"""

    timestamp: float
    rss_mb: float  # 物理内存使用(MB)
    vms_mb: float  # 虚拟内存使用(MB)
    percent: float  # 内存使用百分比
    available_mb: float  # 可用内存(MB)
    gc_count: Dict[int, int] = field(default_factory=dict)  # GC统计

    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp,
            "rss_mb": self.rss_mb,
            "vms_mb": self.vms_mb,
            "percent": self.percent,
            "available_mb": self.available_mb,
            "gc_count": self.gc_count,
        }


class ObjectPool(Generic[T]):
    """
    通用对象池

    减少对象创建和销毁的开销
    """

    def __init__(
        self,
        factory: Callable[[], T],
        max_size: int = 100,
        reset_func: Optional[Callable[[T], None]] = None,
    ):
        """
        初始化对象池

        Args:
            factory: 对象创建工厂函数
            max_size: 池最大大小
            reset_func: 对象重置函数
        """
        self.factory = factory
        self.max_size = max_size
        self.reset_func = reset_func
        self._pool: List[T] = []
        self._lock = threading.Lock()

        # 统计信息
        self.created_count = 0
        self.reused_count = 0
        self.reset_count = 0

    def acquire(self) -> T:
        """获取对象"""
        with self._lock:
            if self._pool:
                obj = self._pool.pop()
                self.reused_count += 1
                return obj
            else:
                obj = self.factory()
                self.created_count += 1
                return obj

    def release(self, obj: T):
        """释放对象回池"""
        with self._lock:
            if len(self._pool) < self.max_size:
                # 重置对象状态
                if self.reset_func:
                    try:
                        self.reset_func(obj)
                        self.reset_count += 1
                    except Exception as e:
                        logger.warning(f"对象重置失败: {e}")
                        return  # 不放回池中

                self._pool.append(obj)

    def get_stats(self) -> Dict[str, Any]:
        """获取池统计信息"""
        with self._lock:
            return {
                "pool_size": len(self._pool),
                "max_size": self.max_size,
                "created_count": self.created_count,
                "reused_count": self.reused_count,
                "reset_count": self.reset_count,
                "reuse_rate": self.reused_count
                / max(1, self.created_count + self.reused_count)
                * 100,
            }

    def clear(self):
        """清空对象池"""
        with self._lock:
            self._pool.clear()


class WeakReferenceManager:
    """
    弱引用管理器

    管理对象的弱引用，防止循环引用导致的内存泄漏
    """

    def __init__(self):
        """初始化弱引用管理器"""
        self._weak_refs: Dict[str, weakref.ref] = {}
        self._callbacks: Dict[str, List[Callable]] = {}
        self._lock = threading.Lock()

    def register(
        self, obj_id: str, obj: Any, callback: Optional[Callable] = None
    ) -> weakref.ref:
        """注册弱引用"""

        def cleanup_callback(ref):
            with self._lock:
                if obj_id in self._weak_refs:
                    del self._weak_refs[obj_id]

                # 执行清理回调
                if obj_id in self._callbacks:
                    for cb in self._callbacks[obj_id]:
                        try:
                            cb()
                        except Exception as e:
                            logger.warning(f"弱引用清理回调失败: {e}")
                    del self._callbacks[obj_id]

        with self._lock:
            weak_ref = weakref.ref(obj, cleanup_callback)
            self._weak_refs[obj_id] = weak_ref

            if callback:
                if obj_id not in self._callbacks:
                    self._callbacks[obj_id] = []
                self._callbacks[obj_id].append(callback)

            return weak_ref

    def get(self, obj_id: str) -> Optional[Any]:
        """获取弱引用对象"""
        with self._lock:
            if obj_id in self._weak_refs:
                return self._weak_refs[obj_id]()
            return None

    def remove(self, obj_id: str):
        """移除弱引用"""
        with self._lock:
            if obj_id in self._weak_refs:
                del self._weak_refs[obj_id]
            if obj_id in self._callbacks:
                del self._callbacks[obj_id]

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            alive_count = sum(
                1 for ref in self._weak_refs.values() if ref() is not None
            )
            return {
                "total_refs": len(self._weak_refs),
                "alive_refs": alive_count,
                "dead_refs": len(self._weak_refs) - alive_count,
            }


class MemoryMonitor:
    """
    内存监控器

    监控内存使用情况并提供优化建议
    """

    def __init__(self, check_interval: float = 30.0):
        """初始化内存监控器"""
        self.check_interval = check_interval
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None

        # 内存历史数据
        self._memory_history: List[MemoryMetrics] = []
        self._max_history = 1000

        # 阈值配置
        self._warning_threshold = 80.0  # 内存使用超过80%警告
        self._critical_threshold = 95.0  # 内存使用超过95%严重

        # 回调函数
        self._warning_callbacks: List[Callable] = []
        self._critical_callbacks: List[Callable] = []

        logger.info("内存监控器初始化完成")

    def start_monitoring(self):
        """开始监控"""
        if self._monitoring:
            logger.warning("内存监控已在运行")
            return

        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("内存监控已启动")

    def stop_monitoring(self):
        """停止监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        logger.info("内存监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                metrics = self._collect_memory_metrics()
                self._memory_history.append(metrics)

                # 限制历史数据大小
                if len(self._memory_history) > self._max_history:
                    self._memory_history = self._memory_history[
                        -self._max_history // 2 :
                    ]

                # 检查阈值
                self._check_thresholds(metrics)

                time.sleep(self.check_interval)

            except Exception as e:
                logger.error(f"内存监控出错: {e}")
                time.sleep(5.0)

    def _collect_memory_metrics(self) -> MemoryMetrics:
        """收集内存指标"""
        try:
            # 系统内存信息
            memory_info = psutil.virtual_memory()

            # 进程内存信息
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()

            # GC统计
            gc_stats = {}
            for i in range(3):
                gc_stats[i] = gc.get_count()[i]

            return MemoryMetrics(
                timestamp=time.time(),
                rss_mb=process_memory.rss / 1024 / 1024,
                vms_mb=process_memory.vms / 1024 / 1024,
                percent=memory_info.percent,
                available_mb=memory_info.available / 1024 / 1024,
                gc_count=gc_stats,
            )

        except Exception as e:
            logger.error(f"收集内存指标失败: {e}")
            return MemoryMetrics(
                timestamp=time.time(), rss_mb=0, vms_mb=0, percent=0, available_mb=0
            )

    def _check_thresholds(self, metrics: MemoryMetrics):
        """检查阈值"""
        if metrics.percent >= self._critical_threshold:
            logger.critical(f"内存使用严重: {metrics.percent:.1f}%")
            for callback in self._critical_callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    logger.error(f"严重内存回调失败: {e}")

        elif metrics.percent >= self._warning_threshold:
            logger.warning(f"内存使用警告: {metrics.percent:.1f}%")
            for callback in self._warning_callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    logger.error(f"内存警告回调失败: {e}")

    def register_warning_callback(self, callback: Callable[[MemoryMetrics], None]):
        """注册警告回调"""
        self._warning_callbacks.append(callback)

    def register_critical_callback(self, callback: Callable[[MemoryMetrics], None]):
        """注册严重回调"""
        self._critical_callbacks.append(callback)

    def get_current_metrics(self) -> MemoryMetrics:
        """获取当前内存指标"""
        return self._collect_memory_metrics()

    def get_memory_trend(self, minutes: int = 30) -> Dict[str, Any]:
        """获取内存趋势"""
        if not self._memory_history:
            return {"error": "没有历史数据"}

        cutoff_time = time.time() - minutes * 60
        recent_metrics = [m for m in self._memory_history if m.timestamp >= cutoff_time]

        if not recent_metrics:
            return {"error": "没有最近的数据"}

        # 计算趋势
        rss_values = [m.rss_mb for m in recent_metrics]
        percent_values = [m.percent for m in recent_metrics]

        return {
            "time_range_minutes": minutes,
            "data_points": len(recent_metrics),
            "rss_trend": {
                "min": min(rss_values),
                "max": max(rss_values),
                "avg": sum(rss_values) / len(rss_values),
                "current": rss_values[-1] if rss_values else 0,
            },
            "percent_trend": {
                "min": min(percent_values),
                "max": max(percent_values),
                "avg": sum(percent_values) / len(percent_values),
                "current": percent_values[-1] if percent_values else 0,
            },
        }


class MemoryOptimizer:
    """
    内存优化器主类

    协调各种内存优化策略
    """

    def __init__(self):
        """初始化内存优化器"""
        self.logger = get_logger(self.__class__.__name__)

        # 组件初始化
        self.monitor = MemoryMonitor()
        self.weak_ref_manager = WeakReferenceManager()

        # 对象池注册表
        self._object_pools: Dict[str, ObjectPool] = {}

        # 优化策略配置
        self._strategies = {
            MemoryStrategy.OBJECT_POOLING: True,
            MemoryStrategy.WEAK_REFERENCES: True,
            MemoryStrategy.LAZY_LOADING: True,
        }

        # 注册内存警告回调
        self.monitor.register_warning_callback(self._handle_memory_warning)
        self.monitor.register_critical_callback(self._handle_memory_critical)

        self.logger.info("内存优化器初始化完成")

    def start_optimization(self):
        """开始内存优化"""
        self.monitor.start_monitoring()
        self.logger.info("内存优化已启动")

    def stop_optimization(self):
        """停止内存优化"""
        self.monitor.stop_monitoring()
        self.logger.info("内存优化已停止")

    def register_object_pool(
        self,
        pool_name: str,
        factory: Callable,
        max_size: int = 100,
        reset_func: Optional[Callable] = None,
    ) -> ObjectPool:
        """注册对象池"""
        pool = ObjectPool(factory, max_size, reset_func)
        self._object_pools[pool_name] = pool
        self.logger.info(f"注册对象池: {pool_name}, 最大大小: {max_size}")
        return pool

    def get_object_pool(self, pool_name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self._object_pools.get(pool_name)

    def force_garbage_collection(self) -> Dict[str, Any]:
        """强制垃圾回收"""
        before_metrics = self.monitor.get_current_metrics()

        # 执行垃圾回收
        collected = gc.collect()

        after_metrics = self.monitor.get_current_metrics()

        result = {
            "collected_objects": collected,
            "memory_before_mb": before_metrics.rss_mb,
            "memory_after_mb": after_metrics.rss_mb,
            "memory_freed_mb": before_metrics.rss_mb - after_metrics.rss_mb,
        }

        self.logger.info(f"强制垃圾回收完成", **result)
        return result

    def _handle_memory_warning(self, metrics: MemoryMetrics):
        """处理内存警告"""
        self.logger.warning(f"内存使用警告，开始优化: {metrics.percent:.1f}%")

        # 执行轻量级优化
        self._cleanup_weak_references()
        self._suggest_gc()

    def _handle_memory_critical(self, metrics: MemoryMetrics):
        """处理内存严重情况"""
        self.logger.critical(f"内存使用严重，执行紧急优化: {metrics.percent:.1f}%")

        # 执行激进优化
        self._emergency_cleanup()
        self.force_garbage_collection()

    def _cleanup_weak_references(self):
        """清理弱引用"""
        # 触发弱引用清理
        gc.collect()
        stats = self.weak_ref_manager.get_stats()
        self.logger.debug(f"弱引用清理: {stats}")

    def _suggest_gc(self):
        """建议垃圾回收"""
        # 设置更激进的GC阈值
        gc.set_threshold(100, 5, 5)
        gc.collect()

    def _emergency_cleanup(self):
        """紧急清理"""
        # 清空所有对象池
        for pool_name, pool in self._object_pools.items():
            pool.clear()
            self.logger.info(f"清空对象池: {pool_name}")

        # 强制垃圾回收
        for _ in range(3):
            gc.collect()

    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        current_metrics = self.monitor.get_current_metrics()
        memory_trend = self.monitor.get_memory_trend()

        # 对象池统计
        pool_stats = {}
        for pool_name, pool in self._object_pools.items():
            pool_stats[pool_name] = pool.get_stats()

        # 弱引用统计
        weak_ref_stats = self.weak_ref_manager.get_stats()

        return {
            "current_memory": current_metrics.to_dict(),
            "memory_trend": memory_trend,
            "object_pools": pool_stats,
            "weak_references": weak_ref_stats,
            "gc_stats": {"counts": gc.get_count(), "thresholds": gc.get_threshold()},
        }


# 全局内存优化器实例
_memory_optimizer = None
_optimizer_lock = threading.Lock()


def get_memory_optimizer() -> MemoryOptimizer:
    """获取全局内存优化器实例"""
    global _memory_optimizer
    with _optimizer_lock:
        if _memory_optimizer is None:
            _memory_optimizer = MemoryOptimizer()
        return _memory_optimizer


# 装饰器函数
def with_object_pool(pool_name: str, factory: Callable = None, max_size: int = 100):
    """对象池装饰器"""

    def decorator(cls):
        optimizer = get_memory_optimizer()

        # 如果没有提供工厂函数，使用类构造函数
        if factory is None:
            pool_factory = cls
        else:
            pool_factory = factory

        # 注册对象池
        optimizer.register_object_pool(pool_name, pool_factory, max_size)

        return cls

    return decorator
