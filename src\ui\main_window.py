#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口模块

该模块提供应用程序主窗口实现：
1. 主窗口布局和组件管理
2. 事件处理和业务逻辑协调
3. UI组件生命周期管理
4. 依赖注入和模块协调

采用现代化架构设计：
- 接口驱动开发
- 依赖注入管理
- 事件驱动通信
- 组件化UI设计

作者: AI助手
日期: 2023-06-01
版本: 2.0.0
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import time
import traceback
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable, Union, Set, cast

# 导入接口和基础组件
from .interfaces import IMainWindow, IUIFactory, IPanel, IStatusDisplay
from .status_bar import StatusBar
from .file_tree import FileTreePanel
from .task_overview_panel import TaskOverviewPanel
from .resizable_paned_window import HorizontalPanedWindow
from .rename_panel import RenamePanel
from .duplicate_panel import DuplicatePanel
from .junk_panel import JunkPanel
from .whitelist_panel import WhitelistPanel
from .settings_panel import SettingsPanel
from ..core.dependency_injection import DependencyContainer, resolve, register
from ..utils.logger import get_logger
from ..utils.event_system import EventSystem
from ..utils.async_manager import AsyncManager
from ..utils.config_loader import ConfigLoader
from ..utils.interrupt_manager import get_interrupt_manager, InterruptReason
from ..utils.progress_feedback import ProgressTracker as EnhancedProgressTracker, TaskState
from ..utils.unified_progress_manager import get_progress_manager, TaskType, TaskStatus, TaskProgress
from src.utils.format_utils import format_size, _normalize_path

# 导入核心模块
from ..data.db_manager import MongoDBManager
from ..rule_engine import RuleEngine
from ..core.file_scanner import FileScanner
from ..video_analyzer import VideoAnalyzer
from ..core.file_operations import FileOperations
from ..ui.factory import StandardUIFactory, IUIFactory
from src.core.rule_engine import RuleEngine
from src.core.duplicate_finder import DuplicateFinder

logger = get_logger(__name__)


class MainWindow(IMainWindow):
    """
    智能文件管理器主窗口实现
    
    提供应用程序主窗口功能，采用依赖注入和事件驱动架构
    """
    
    def __init__(self, root: tk.Tk, ui_factory: IUIFactory, event_system: EventSystem, async_manager: AsyncManager, logger: Any, config_loader: ConfigLoader, db_manager: MongoDBManager, rule_engine: RuleEngine, file_scanner: FileScanner, video_analyzer: VideoAnalyzer, file_operations: FileOperations) -> None:
        # 必须最先初始化UI主属性，防止流程未初始化时报错
        self.main_frame = None
        self.notebook = None
        self.left_frame = None
        self.right_frame = None
        self.vertical_paned = None
        self.information_frame = None
        self.root = root
        self.ui_factory = ui_factory
        self.logger = logger
        self.event_system = event_system
        self.async_manager = async_manager
        self.config_loader = config_loader
        self.db_manager = db_manager
        self.rule_engine = rule_engine
        self.file_scanner = file_scanner
        self.video_analyzer = video_analyzer
        self.file_operations = file_operations
        from src.core.dependency_injection import get_container
        self.container = get_container()
        # 新增：初始化任务队列、运行标志、结果队列、批处理参数，防止linter报错
        import queue, threading
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.task_running = threading.Event()
        self.current_task_id = None  # 新增：跟踪当前任务ID
        self.interrupt_event = threading.Event()  # 新增：中断事件
        self.file_tree_loading = False  # 新增：文件树加载状态
        self.progress_manager = get_progress_manager()  # 新增：统一进度管理器

        # 新增：初始化统一任务管理器
        from src.utils.unified_task_manager import UnifiedTaskManager
        self.unified_task_manager = UnifiedTaskManager()

        self.fast_batch_process_size = 50
        self.batch_process_size = 10
        # 新增：初始化进度相关属性，修复linter错误
        self.task_label = tk.StringVar(value="当前任务: 无")
        self.subtask_label = tk.StringVar(value="子任务: 无")
        self.progress_var = tk.DoubleVar(value=0)
        # 必须最先初始化Debug相关属性，避免后续方法调用时未定义
        self._widget_registry = {}
        self._debug_labels = {}
        self.debug_mode = False
        # 初始化核心属性
        self.update_interval = 100  # 结果处理更新间隔（毫秒）
        self.fast_update_interval = 20  # 快速处理更新间隔（毫秒），修复linter错误
        if self.logger:
            self.logger.debug(f"[Init] 核心属性初始化验证 - update_interval: {hasattr(self, 'update_interval')}")
        # 初始化窗口、主题、UI组件、事件绑定、核心服务
        self._setup_window()
        self._setup_theme()
        self.create_widgets()
        self.bind_events()
        self._initialize_services()
        self._start_worker_threads()
        # 应用全局字体到所有控件
        font_size = 10
        if hasattr(self, 'config_loader') and self.config_loader:
            try:
                settings_config = self.config_loader.get_config("settings")
                if settings_config and 'general' in settings_config:
                    font_size = settings_config['general'].get('font_size', 10)
            except Exception:
                pass
        font = ("Helvetica", font_size)
        if hasattr(self.ui_factory, 'apply_font_to_widget'):
            self.ui_factory.apply_font_to_widget(self.main_frame, font)
        if self.logger:
            self.logger.info("[主窗口] 初始化完成 - 所有组件已就绪")
        self.current_task = None
    
    def _setup_dependencies(self) -> None:
        # 注册/获取全局依赖
        if not self.container.is_registered(get_logger):
            logger = get_logger(name="MainWindow")
            register(get_logger, logger)
        self.logger = resolve(get_logger)
        if not self.container.is_registered(EventSystem):
            event_system = EventSystem()
            register(EventSystem, event_system)
        self.event_system = resolve(EventSystem)
        if not self.container.is_registered(AsyncManager):
            async_manager = AsyncManager()
            register(AsyncManager, async_manager)
        self.async_manager = resolve(AsyncManager)
        if not self.container.is_registered(ConfigLoader):
            import os
            config_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../config'))
            config_loader = ConfigLoader(config_dir)
            register(ConfigLoader, config_loader)
        self.config_loader = resolve(ConfigLoader)
    
    def _resolve_or_register_core_services(self) -> None:
        # 数据库管理器
        if not self.container.is_registered(MongoDBManager):
            self.db_manager = MongoDBManager()
            register(MongoDBManager, self.db_manager)
        else:
            self.db_manager = resolve(MongoDBManager)
        # 规则引擎
        if not self.container.is_registered(RuleEngine):
            self.rule_engine = RuleEngine()
            register(RuleEngine, self.rule_engine)
        else:
            self.rule_engine = resolve(RuleEngine)
        # 文件扫描器
        if not self.container.is_registered(FileScanner):
            from src.core.factory import ModuleFactory
            module_factory = ModuleFactory(db_manager=self.db_manager)
            self.file_scanner = module_factory.create_file_scanner()
            register(FileScanner, self.file_scanner)
        else:
            self.file_scanner = resolve(FileScanner)
            # 确保db_manager已绑定
            if self.db_manager and (not hasattr(self.file_scanner, 'db_manager') or self.file_scanner.db_manager is None):
                self.file_scanner.set_db_manager(self.db_manager)
        # 视频分析器
        if not self.container.is_registered(VideoAnalyzer):
            self.video_analyzer = VideoAnalyzer()
            register(VideoAnalyzer, self.video_analyzer)
        else:
            self.video_analyzer = resolve(VideoAnalyzer)
        # 文件操作
        if not self.container.is_registered(FileOperations):
            self.file_operations = FileOperations()
            register(FileOperations, self.file_operations)
        else:
            self.file_operations = resolve(FileOperations)
        # 判空保护
        for dep_name in ["logger", "event_system", "async_manager", "config_loader", "db_manager", "rule_engine", "file_scanner", "video_analyzer", "file_operations"]:
            if getattr(self, dep_name) is None:
                raise RuntimeError(f"依赖 {dep_name} 未初始化，无法启动主窗口！")
        if self.logger:
            self.logger.info("所有核心依赖已通过依赖注入容器初始化并赋值")
    
    def _setup_window(self) -> None:
        """设置窗口属性"""
        # 配置高DPI支持
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass
            
        # 获取屏幕DPI缩放比例
        self.dpi_scale = self.root.winfo_fpixels('1i') / 72
        
        # 根据DPI缩放调整初始窗口大小
        init_width = int(1200 * self.dpi_scale)
        init_height = int(800 * self.dpi_scale)
        min_width = int(800 * self.dpi_scale)
        min_height = int(600 * self.dpi_scale)
        
        self.root.title("智能文件管理器 v2.0")
        self.root.geometry(f"{init_width}x{init_height}")
        self.root.minsize(min_width, min_height)
        
    def _setup_theme(self) -> None:
        """设置主题"""
        # 获取主题管理器并应用主题
        theme_manager = self.ui_factory.get_theme_manager() if hasattr(self.ui_factory, 'get_theme_manager') else None
        if theme_manager and hasattr(theme_manager, 'get_theme'):
            self._theme = theme_manager.get_theme()
        else:
            self._theme = {}
        # 创建并保存style对象为实例属性
        if not hasattr(self, 'style'):
            self.style = ttk.Style()
            self.style.theme_use("clam")
            if theme_manager and hasattr(theme_manager, 'apply_theme_to_style'):
                theme_manager.apply_theme_to_style(self.style)
        # 应用字体大小设置
        try:
            if hasattr(self, 'config_loader') and self.config_loader:
                # 尝试从设置配置中加载字体大小
                try:
                    settings_config = self.config_loader.get_config("settings")
                    self.logger.info(f"[字体调试] settings_config: {settings_config}")
                    if settings_config and 'general' in settings_config:
                        font_size = settings_config['general'].get('font_size', 10)
                        self.logger.info(f"[字体调试] 读取到的字体大小: {font_size}")
                        if hasattr(self.ui_factory, 'apply_font_size'):
                            self.ui_factory.apply_font_size(font_size)
                            self.logger.info(f"从配置加载字体大小: {font_size}")
                    else:
                        self.logger.warning("[字体调试] settings_config中未找到general或font_size，使用默认值10")
                        if hasattr(self.ui_factory, 'apply_font_size'):
                            self.ui_factory.apply_font_size(10)
                except Exception as e:
                    self.logger.warning(f"加载字体大小配置失败，使用默认值: {e}")
                    # 使用默认字体大小
                    if hasattr(self.ui_factory, 'apply_font_size'):
                        self.ui_factory.apply_font_size(10)
            else:
                self.logger.warning("配置加载器未初始化，使用默认字体大小")
                if hasattr(self.ui_factory, 'apply_font_size'):
                    self.ui_factory.apply_font_size(10)
        except Exception as e:
            self.logger.error(f"应用字体大小设置失败: {e}")
            # 确保使用默认字体大小
            if hasattr(self.ui_factory, 'apply_font_size'):
                self.ui_factory.apply_font_size(10)
    
    def _initialize_services(self) -> None:
        """初始化核心服务"""
        try:
            # 配置目录路径
            self.config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config")
            self.rules_file = os.path.join(self.config_dir, "default_rules.yaml")
            self.backup_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "backup")
            
            # 创建备份目录
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
            
            # 注册界面日志回调函数
            from src.utils.logger import register_ui_log_callback
            register_ui_log_callback(self._on_log_message)
            
            # 初始化核心服务（使用依赖注入）
            self._setup_core_services()
            
            # 订阅事件
            self.subscribe_events()
            
            # 初始化数据库状态监控（数据库管理器已在_setup_core_services中创建）
            self._initialize_db_status_monitor()
            
            if self.logger:
                self.logger.info("[核心服务] 初始化完成 - 所有依赖已注入")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"初始化服务失败: {e}")
            messagebox.showerror("初始化错误", f"初始化服务失败: {e}")
    
    def _on_log_message(self, message: str, level: str) -> None:
        """处理来自日志系统的消息"""
        try:
            # 简化消息处理，避免复杂的正则匹配
            clean_message = message

            # 如果消息包含时间戳格式，尝试提取
            if " - " in message and message.count(" - ") >= 4:
                parts = message.split(" - ")
                if len(parts) >= 5:
                    clean_message = " - ".join(parts[4:])

            # 直接调用日志显示方法
            self.log_message(clean_message, level)

        except Exception as e:
            # 如果处理失败，直接显示原始消息
            try:
                self.log_message(message, level)
            except:
                pass
            print(f"处理日志消息失败: {e}")
    
    def _flush_ui_logs(self) -> None:
        """立即刷新UI日志缓冲区"""
        try:
            from src.utils.logger import flush_ui_logs
            flush_ui_logs()
        except Exception as e:
            print(f"刷新UI日志失败: {e}")
    
    def _clear_ui_logs(self) -> None:
        """清空UI日志缓冲区"""
        try:
            from src.utils.logger import clear_ui_logs
            clear_ui_logs()
        except Exception as e:
            print(f"清空UI日志失败: {e}")

    def diagnose_ui_issues(self):
        """诊断UI问题"""
        self.logger.info("🔍 开始UI问题诊断...")

        all_issues = []

        # 诊断日志系统
        log_issues = self._diagnose_log_system()
        if log_issues:
            all_issues.extend([f"日志系统: {issue}" for issue in log_issues])

        # 诊断状态栏
        status_issues = self._diagnose_status_bar()
        if status_issues:
            all_issues.extend([f"状态栏: {issue}" for issue in status_issues])

        # 诊断任务状态
        task_issues = self._diagnose_task_state()
        if task_issues:
            all_issues.extend([f"任务状态: {issue}" for issue in task_issues])

        # 输出诊断结果
        if all_issues:
            self.logger.warning("⚠️ 发现UI问题:")
            for issue in all_issues:
                self.logger.warning(f"  - {issue}")

            # 在日志窗口中也显示
            self.log_message("UI诊断发现问题:", "warning")
            for issue in all_issues:
                self.log_message(f"  - {issue}", "warning")
        else:
            self.logger.info("✅ UI诊断完成，未发现问题")
            self.log_message("UI诊断完成，未发现问题", "success")

        return all_issues

    def _diagnose_log_system(self):
        """诊断日志系统状态"""
        issues = []

        # 检查日志文本框
        if not hasattr(self, 'log_text') or not self.log_text:
            issues.append("日志文本框未创建")
        else:
            try:
                # 测试日志文本框是否可用
                state = self.log_text.cget('state')
                if state == 'disabled':
                    # disabled状态是正常的，表示只读模式
                    pass  # 不报告为问题
                else:
                    issues.append(f"日志文本框状态异常: {state} (应为disabled)")
            except Exception as e:
                issues.append(f"日志文本框检查失败: {e}")

        # 检查日志回调
        try:
            from src.utils.logger import get_ui_log_callback
            callback = get_ui_log_callback()
            if not callback:
                issues.append("日志回调未注册")
            # 日志回调已注册是正常状态，不是问题
        except Exception as e:
            issues.append(f"日志回调检查失败: {e}")

        return issues

    def _diagnose_status_bar(self):
        """诊断状态栏状态"""
        issues = []

        # 检查状态栏对象
        if not hasattr(self, 'status_bar') or not self.status_bar:
            issues.append("状态栏未创建")
            return issues

        # 检查进度条
        if not hasattr(self.status_bar, 'progress_var') or not self.status_bar.progress_var:
            issues.append("进度条变量未创建")
        # 进度条的值是正常状态信息，不是问题

        # 检查中止按钮
        if not hasattr(self.status_bar, 'stop_button') or not self.status_bar.stop_button:
            issues.append("中止按钮未创建")
        # 中止按钮的状态是正常状态信息，不是问题

        # 检查回调函数
        if not hasattr(self.status_bar, '_stop_callback') or not self.status_bar._stop_callback:
            issues.append("中止按钮回调未设置")
        # 回调已设置是正常状态，不是问题

        return issues

    def _diagnose_task_state(self):
        """诊断任务状态"""
        issues = []

        # 检查任务运行状态
        if not hasattr(self, 'task_running') or not self.task_running:
            issues.append("任务运行状态未初始化")
        # 任务运行状态是正常状态信息，不是问题

        # 检查当前任务变量
        if not hasattr(self, 'current_task'):
            issues.append("当前任务变量未初始化")
        # 当前任务的具体值是正常状态信息，不是问题

        # 检查中断事件
        if not hasattr(self, 'interrupt_event') or not self.interrupt_event:
            issues.append("中断事件未初始化")
        # 中断事件的状态是正常状态信息，不是问题

        return issues

    def _setup_core_services(self) -> None:
        """设置核心服务"""
        try:
            # 导入服务生命周期枚举
            from src.core.dependency_injection import ServiceLifetime
            
            # 注册核心服务
            if not self.container.is_registered(MongoDBManager):
                try:
                    if self.logger:
                        self.logger.info("开始创建MongoDBManager实例...")
                    self.db_manager = MongoDBManager(
                        db_name="fileinfodb",
                        collection_name="files",
                        max_pool_size=100,
                        min_pool_size=10,
                        host="localhost",
                        port=27017,
                        max_retries=3,
                        retry_delay=1.0,
                        connect_timeout=2000,
                        server_timeout=3000,
                        progress_callback=self._on_db_progress,
                        event_system=self.event_system
                    )
                    register(MongoDBManager, self.db_manager, lifetime=ServiceLifetime.SINGLETON)
                    if self.logger:
                        self.logger.info("MongoDBManager实例创建成功")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"创建MongoDBManager实例失败: {e}")
                        self.logger.error(f"错误详情: {traceback.format_exc()}")
                    self.db_manager = None
            else:
                self.db_manager = resolve(MongoDBManager)
                if self.logger:
                    self.logger.info("从依赖注入容器获取MongoDBManager实例")
            
            if not self.container.is_registered(RuleEngine):
                self.rule_engine = RuleEngine(self.rules_file)
                register(RuleEngine, self.rule_engine, lifetime=ServiceLifetime.SINGLETON)
            else:
                self.rule_engine = resolve(RuleEngine)
            
            if not self.container.is_registered(VideoAnalyzer):
                self.video_analyzer = VideoAnalyzer()
                register(VideoAnalyzer, self.video_analyzer, lifetime=ServiceLifetime.SINGLETON)
            else:
                self.video_analyzer = resolve(VideoAnalyzer)
            
            if not self.container.is_registered(FileScanner):
                from src.utils.async_task_manager import AsyncTaskManager
                async_task_manager = AsyncTaskManager()
                self.file_scanner = FileScanner(db_manager=self.db_manager, async_task_manager=async_task_manager)
                register(FileScanner, self.file_scanner, lifetime=ServiceLifetime.SINGLETON)
            else:
                self.file_scanner = resolve(FileScanner)
                # 确保数据库管理器已设置
                if hasattr(self, 'db_manager') and self.db_manager and not hasattr(self.file_scanner, 'db_manager'):
                    self.file_scanner.set_db_manager(self.db_manager)
            
            if not self.container.is_registered(FileOperations):
                self.file_operations = FileOperations(self.backup_dir)
                register(FileOperations, self.file_operations, lifetime=ServiceLifetime.SINGLETON)
            else:
                self.file_operations = resolve(FileOperations)
            
            if self.logger:
                self.logger.info("核心服务设置完成")
            
            # 初始化数据库连接状态
            self._check_database_connection_status()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"设置核心服务失败: {e}")
            raise
    
    def _check_database_connection_status(self) -> None:
        """检查数据库连接状态"""
        try:
            if not hasattr(self, 'db_manager') or self.db_manager is None:
                if self.logger:
                    self.logger.warning("数据库管理器未初始化或为None，无法检查连接状态")
                self.update_mongodb_connection_status(False)
                return
            
            if self.logger:
                self.logger.info("开始检查数据库连接状态...")
            
            # 检查数据库连接状态
            try:
                if self.db_manager is not None and self.db_manager.check_connection_health():
                    if self.logger:
                        self.logger.info("数据库连接状态检查成功：已连接")
                    self.update_mongodb_connection_status(True)
                    
                    # 数据库连接成功，文件树将由启动时自动加载机制统一处理
                    if self.logger:
                        self.logger.info("数据库连接成功，文件树将由启动时自动加载机制处理")
                else:
                    if self.logger:
                        self.logger.warning("数据库连接状态检查失败：未连接")
                    self.update_mongodb_connection_status(False)
            except Exception as e:
                if self.logger:
                    self.logger.error(f"检查数据库连接状态时发生异常: {e}")
                    self.logger.error(f"异常详情: {traceback.format_exc()}")
                self.update_mongodb_connection_status(False)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"检查数据库连接状态失败: {e}")
                self.logger.error(f"错误详情: {traceback.format_exc()}")
            self.update_mongodb_connection_status(False)
    
    def _start_worker_threads(self) -> None:
        """启动工作线程"""
        # 启动工作线程
        self.worker_thread = threading.Thread(target=self.worker, daemon=True)
        self.worker_thread.start()

        # 延迟启动结果处理，避免在初始化时阻塞
        self.root.after(100, self.process_results)
    
    def worker(self) -> None:
        """后台任务线程实现"""
        while True:
            try:
                # 从任务队列获取任务
                task = self.task_queue.get()
                
                if task is None:  # 退出信号
                    break
                
                # 设置当前任务和运行状态
                self.current_task = task
                self.task_running.set()
                
                # 启用中止按钮
                if self.status_bar:
                    self.status_bar.enable_stop_button()
                
                # 解析任务
                task_type = task.get("type")
                task_data = task.get("data")
                
                # 根据任务类型执行相应操作
                if task_type == "scan_directory":
                    self.do_scan_directory(task_data)
                elif task_type == "find_duplicate_files":
                    self.do_find_duplicate_files(task_data)
                elif task_type == "find_same_name_videos":
                    self.do_find_same_name_videos()
                elif task_type == "find_junk_files":
                    self.do_find_junk_files()
                elif task_type == "find_whitelist_files":
                    self.do_find_whitelist_files()
                elif task_type == "apply_rename_rules":
                    self.do_apply_rename_rules(task_data)
                elif task_type == "delete_files":
                    self.do_delete_files(task_data)
                elif task_type == "move_files":
                    self.do_move_files(task_data)
                elif task_type == "clear_database":
                    self.do_clear_database()
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"工作线程错误: {e}\n{traceback.format_exc()}")
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": 100,
                        "status": f"任务执行出错: {str(e)}",
                        "task": "错误",
                        "subtask": str(e),
                        "log": f"任务执行出错: {str(e)}",
                        "log_level": "error"
                    }
                })
            finally:
                # 清理任务状态
                self.current_task = None
                self.current_task_id = None  # 清除任务ID
                self.task_running.clear()

                # 禁用中止按钮
                if self.status_bar:
                    self.status_bar.disable_stop_button()
                    
                # 任务完成后，立即处理所有剩余的日志消息
                self.flush_result_queue()
                
                # 立即刷新UI日志缓冲区，确保所有日志都显示出来
                self._flush_ui_logs()

    def process_results(self) -> None:
        """处理结果队列中的结果"""
        # 检查是否有任务正在运行（在try外定义，避免finally中未定义）
        is_task_running = self.task_running.is_set()

        try:
            
            # 如果任务不在运行中，增加一次处理的消息数量，加快处理速度
            max_process = self.fast_batch_process_size if not is_task_running else self.batch_process_size
            
            # 批量收集结果
            results = []
            while not self.result_queue.empty() and len(results) < max_process:
                try:
                    result = self.result_queue.get_nowait()
                    if result:
                        results.append(result)
                    self.result_queue.task_done()
                except queue.Empty:
                    break
            
            # 优化结果处理：如果任务已经结束，过滤掉过时的进度信息
            if not is_task_running and len(results) > 5:
                # 找到完成类消息和进度类消息
                completion_results = [r for r in results if r.get("type") in 
                                    ("scan_complete", "operation_complete", "refresh_file_tree")]
                progress_results = [r for r in results if r.get("type") == "progress"]
                
                if completion_results and len(progress_results) > 1:
                    # 如果同时有完成消息和多条进度消息，只保留最后一条进度消息和所有完成消息
                    filtered_results = [progress_results[-1]] + completion_results
                    # 加上非进度和非完成的其他消息
                    other_results = [r for r in results if r.get("type") not in 
                                   ("progress", "scan_complete", "operation_complete", "refresh_file_tree")]
                    results = filtered_results + other_results
                    
                    # 在日志中提示合并了进度消息
                    if len(progress_results) > 1:
                        self.logger.debug(f"UI更新优化：合并了 {len(progress_results)-1} 条进度消息")
            
            # 准备批量更新日志
            log_messages = []
            
            # 处理收集到的结果
            for result in results:
                result_type = result.get("type")
                result_data = result.get("data", {})
                
                if result_type == "progress":
                    self.update_progress(result_data.get("progress", 0), result_data.get("status", ""))
                    if self.status_bar and hasattr(self.status_bar, 'set_task_info'):
                        self.status_bar.set_task_info(result_data.get("task", ""), result_data.get("subtask", ""))
                    # 只同步到status_bar
                    if "elapsed_time" in result_data:
                        if self.status_bar and hasattr(self.status_bar, 'elapsed_time_var'):
                            self.status_bar.elapsed_time_var.set(result_data["elapsed_time"])
                    # 收集日志消息，稍后批量更新
                    if "log" in result_data:
                        log_msg = result_data["log"]
                        if "elapsed_time" in result_data:
                            log_msg += f" | 耗时: {result_data['elapsed_time']}"
                        log_messages.append((log_msg, result_data.get("log_level", "info")))
                elif result_type == "scan_complete":
                    # 扫描完成，更新文件树
                    self.logger.info("收到扫描完成消息，更新文件树")
                    self.load_all_files_from_database()
                    
                    # 文件数统计由文件树面板统一显示，无需在主窗口重复设置
                    
                    # 收集完成信息日志
                    log_messages.append((f"扫描完成，共扫描 {result_data.get('file_count', 0)} 个文件，其中视频文件 {result_data.get('video_count', 0)} 个", "success"))
                elif result_type == "duplicate_files":
                    # 处理重复文件查找结果
                    if hasattr(self, 'duplicate_panel') and self.duplicate_panel:
                        try:
                            # 直接调用更新方法，不需要类型检查
                            self.duplicate_panel.update_duplicate_groups(result_data)
                            log_msg = f"成功更新重复文件组，找到 {result_data.get('total_groups', 0)} 组重复文件"
                            self.logger.info(log_msg)
                            log_messages.append((log_msg, "info"))
                        except Exception as e:
                            error_msg = f"更新重复文件组失败: {e}"
                            self.logger.error(error_msg)
                            log_messages.append((error_msg, "error"))
                            import traceback
                            self.logger.error(f"错误详情: {traceback.format_exc()}")
                elif result_type == "duplicate_groups":
                    # 处理新的重复文件查找结果格式
                    if hasattr(self, 'duplicate_panel') and self.duplicate_panel:
                        try:
                            self.duplicate_panel.update_duplicate_groups(result_data)
                            log_msg = f"成功更新重复文件组，找到 {result_data.get('total_groups', 0)} 组重复文件"
                            self.logger.info(log_msg)
                            log_messages.append((log_msg, "info"))
                        except Exception as e:
                            error_msg = f"更新重复文件组失败: {e}"
                            self.logger.error(error_msg)
                            log_messages.append((error_msg, "error"))
                            import traceback
                            self.logger.error(f"错误详情: {traceback.format_exc()}")
                elif result_type == "operation_complete":
                    # 显示操作完成消息
                    message = result_data.get("message", "操作完成")
                    success = result_data.get("success", True)
                    if success:
                        messagebox.showinfo("成功", message)
                        log_messages.append((message, "success"))
                    else:
                        messagebox.showerror("错误", message)
                        log_messages.append((message, "error"))
                elif result_type == "whitelist_files":
                    # 处理白名单文件查找结果
                    if hasattr(self, 'whitelist_panel') and self.whitelist_panel:
                        try:
                            # 调用白名单面板的update_ui方法
                            self.whitelist_panel.update_ui({"whitelist_files": result_data})
                            log_msg = f"成功更新白名单文件显示，找到 {len(result_data)} 个白名单文件"
                            self.logger.info(log_msg)
                            log_messages.append((log_msg, "info"))
                        except Exception as e:
                            error_msg = f"更新白名单文件显示失败: {e}"
                            self.logger.error(error_msg)
                            log_messages.append((error_msg, "error"))
                            import traceback
                            self.logger.error(f"错误详情: {traceback.format_exc()}")
                elif result_type == "refresh_file_tree":
                    # 刷新文件树
                    self.logger.info("收到刷新文件树消息")
                    force_refresh = result_data.get("force_refresh", False)
                    if force_refresh:
                        # 强制刷新：先清空再重新加载
                        self.clear_file_tree_display()
                        self.root.after(100, self.load_all_files_from_database)
                    else:
                        self.load_all_files_from_database()
                    if "message" in result_data:
                        log_messages.append((result_data["message"], "info"))

                elif result_type == "clear_file_tree_cache":
                    # 清空文件树缓存和显示
                    self.logger.info("收到清空文件树缓存消息")
                    self.clear_file_tree_display()
                    log_messages.append(("文件树缓存已清空", "info"))
            
            # 批量更新日志（避免频繁更新UI导致卡顿）
            if log_messages:
                self.batch_log_messages(log_messages)
                        
        except Exception as e:
            self.logger.error(f"处理结果队列时出错: {e}\n{traceback.format_exc()}")
        finally:
            try:
                # 如果任务不在运行中且队列不为空，使用更短的间隔快速处理
                next_interval = self.fast_update_interval if not is_task_running and not self.result_queue.empty() else self.update_interval
                # 继续处理结果队列
                if hasattr(self, 'root') and self.root:
                    self.root.after(next_interval, self.process_results)
                else:
                    self.logger.warning("root窗口不存在，停止处理结果队列")
            except Exception as finally_error:
                self.logger.error(f"finally块执行失败: {finally_error}")

    def flush_result_queue(self):
        """快速处理所有剩余的结果队列消息
        
        在任务完成后调用，确保所有日志信息快速更新到UI
        """
        try:
            if self.result_queue.empty():
                return
                
            self.logger.info("执行结果队列快速刷新")
            
            # 收集所有剩余结果
            all_results = []
            while not self.result_queue.empty():
                try:
                    result = self.result_queue.get_nowait()
                    if result:
                        all_results.append(result)
                    self.result_queue.task_done()
                except queue.Empty:
                    break
            
            if not all_results:
                return
                
            # 筛选出最重要的消息（完成消息和最后的进度消息）
            completion_results = [r for r in all_results if r.get("type") in 
                                ("scan_complete", "operation_complete", "refresh_file_tree")]
            progress_results = [r for r in all_results if r.get("type") == "progress"]
            
            important_results = []
            if progress_results:
                important_results.append(progress_results[-1])  # 添加最后一条进度消息
            important_results.extend(completion_results)  # 添加所有完成消息
            
            # 处理重要结果
            log_messages = []
            
            for result in important_results:
                result_type = result.get("type")
                result_data = result.get("data", {})
                
                # 更新进度
                if result_type == "progress":
                    # 设置进度为100%表示完成
                    self.update_progress(100, result_data.get("status", "已完成"))
                    
                    # 收集日志消息
                    if "log" in result_data:
                        log_msg = result_data["log"]
                        if "elapsed_time" in result_data:
                            log_msg += f" | 耗时: {result_data['elapsed_time']}"
                        log_messages.append((log_msg, result_data.get("log_level", "info")))
                
                # 处理完成消息
                elif result_type in ("scan_complete", "operation_complete", "refresh_file_tree"):
                    if result_type == "scan_complete":
                        log_messages.append((f"扫描完成，共扫描 {result_data.get('file_count', 0)} 个文件", "success"))
                    elif result_type == "operation_complete":
                        message = result_data.get("message", "操作完成")
                        log_messages.append((message, "success"))
                    elif result_type == "refresh_file_tree" and "message" in result_data:
                        log_messages.append((result_data["message"], "info"))
            
            # 添加汇总消息
            skipped_count = len(all_results) - len(important_results)
            if skipped_count > 0:
                log_messages.append((f"已优化显示，跳过了 {skipped_count} 条中间过程消息", "info"))
            
            # 批量更新日志
            if log_messages:
                self.batch_log_messages(log_messages)
                
        except Exception as e:
            self.logger.error(f"刷新结果队列失败: {e}\n{traceback.format_exc()}")

    def register_widget(self, widget, name):
        """注册控件及其名称，便于Debug模式显示，并记录原始边框属性"""
        self._widget_registry[widget] = name
        # 记录原始高亮属性（仅对tk控件有效）
        if not hasattr(self, '_widget_highlight_backup'):
            self._widget_highlight_backup = {}
        try:
            orig = {
                'highlightbackground': widget.cget('highlightbackground') if 'highlightbackground' in widget.keys() else None,
                'highlightcolor': widget.cget('highlightcolor') if 'highlightcolor' in widget.keys() else None,
                'highlightthickness': widget.cget('highlightthickness') if 'highlightthickness' in widget.keys() else None,
            }
            self._widget_highlight_backup[widget] = orig
        except Exception:
            pass

    def toggle_debug_mode(self):
        self.debug_mode = not self.debug_mode
        if self.debug_mode:
            self.show_widget_names()
        else:
            self.hide_widget_names()

    def show_widget_names(self):
        for widget, name in self._widget_registry.items():
            if not widget.winfo_ismapped():
                continue
            # 计算标签位置：左上角，稍微偏移
            try:
                x = widget.winfo_x() + 2
                y = widget.winfo_y() + 2
                label = tk.Label(widget.master, text=name, bg='yellow', fg='red', font=('Arial', 8), relief='solid', bd=1)
                label.place(x=x, y=y)
                # 事件绑定：悬浮高亮
                label.bind('<Enter>', lambda e, w=widget: self._highlight_widget(w, True))
                label.bind('<Leave>', lambda e, w=widget: self._highlight_widget(w, False))
                self._debug_labels[widget] = label
            except Exception:
                pass

    def hide_widget_names(self):
        for label in self._debug_labels.values():
            try:
                label.destroy()
            except Exception:
                pass
        self._debug_labels = {}
        # 恢复所有控件原始高亮
        if hasattr(self, '_widget_highlight_backup'):
            for widget, orig in self._widget_highlight_backup.items():
                try:
                    if orig['highlightbackground'] is not None:
                        widget.config(highlightbackground=orig['highlightbackground'])
                    if orig['highlightcolor'] is not None:
                        widget.config(highlightcolor=orig['highlightcolor'])
                    if orig['highlightthickness'] is not None:
                        widget.config(highlightthickness=orig['highlightthickness'])
                except Exception:
                    pass

    def _highlight_widget(self, widget, highlight):
        # 只对tk控件高亮，ttk控件不支持
        try:
            if highlight:
                widget.config(highlightbackground='red', highlightcolor='red', highlightthickness=3)
            else:
                # 恢复原始
                if hasattr(self, '_widget_highlight_backup') and widget in self._widget_highlight_backup:
                    orig = self._widget_highlight_backup[widget]
                    if orig['highlightbackground'] is not None:
                        widget.config(highlightbackground=orig['highlightbackground'])
                    if orig['highlightcolor'] is not None:
                        widget.config(highlightcolor=orig['highlightcolor'])
                    if orig['highlightthickness'] is not None:
                        widget.config(highlightthickness=orig['highlightthickness'])
        except Exception:
            pass

    def create_management_panel(self) -> None:
        """
        创建管理面板 - 目录管理、MongoDB管理、规则管理器（左侧竖直排列）
        只负责left_frame/right_frame内容，不再创建信息区frame。
        """
        # Debug模式按钮和状态面板（放在左侧管理面板顶部）
        debug_status_frame = ttk.Frame(self.left_frame)
        debug_status_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        self.register_widget(debug_status_frame, 'debug_status_frame')
        
        # Debug按钮（缩小）
        debug_btn = ttk.Button(debug_status_frame, text="Debug", command=self.toggle_debug_mode, width=8)
        debug_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.register_widget(debug_btn, 'debug_btn')
        
        # 状态面板
        try:
            from src.ui.status_panel import StatusPanel
            # 获取全局字体配置
            font_size = 10
            if hasattr(self, 'config_loader') and self.config_loader:
                try:
                    settings_config = self.config_loader.get_config("settings")
                    if settings_config and 'general' in settings_config:
                        font_size = settings_config['general'].get('font_size', 10)
                except Exception:
                    pass
            global_font = ("Helvetica", font_size)
            self.status_panel = StatusPanel(debug_status_frame, font=global_font)
            self.status_panel.pack(side=tk.LEFT, fill=tk.X, expand=True)
            # 添加状态指示灯
            self.status_panel.add_status_light("db_connection", "数据库连接", "red", "检查数据库连接状态", 0, 0)
            self.status_panel.add_status_light("hash_completeness", "Hash完整性", "red", "检查数据库中的Hash值完整性", 0, 1)
            # 添加进度条
            self.status_panel.add_progress_bar("hash_progress", "Hash完整性", 0, 2)
            self.register_widget(self.status_panel, 'status_panel')
        except Exception as e:
            self.logger.error(f"创建状态面板失败: {e}")
            # 如果状态面板创建失败，创建一个占位标签
            status_label = ttk.Label(debug_status_frame, text="状态监控未启用")
            status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 1. 目录管理section
        self.dir_manager_frame = ttk.LabelFrame(self.left_frame, text="目录管理", padding=10)
        self.dir_manager_frame.pack(fill=tk.X, padx=10, pady=(5, 10))
        self.register_widget(self.dir_manager_frame, 'dir_manager_frame')

        # 添加目录列表框和按钮
        listbox_frame = ttk.Frame(self.dir_manager_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.register_widget(listbox_frame, 'listbox_frame')

        self.dir_listbox = tk.Listbox(listbox_frame, selectmode=tk.EXTENDED, height=5)
        self.dir_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        self.register_widget(self.dir_listbox, 'dir_listbox')

        dir_list_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.dir_listbox.yview)
        dir_list_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=2, pady=2)
        self.dir_listbox.config(yscrollcommand=dir_list_scrollbar.set)
        self.dir_scrollbar = dir_list_scrollbar # 存储引用
        self.register_widget(dir_list_scrollbar, 'dir_list_scrollbar')

        btn_frame = ttk.Frame(self.dir_manager_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        self.register_widget(btn_frame, 'btn_frame')

        btn_add = ttk.Button(btn_frame, text="添加目录", command=self.add_directory)
        btn_add.grid(row=0, column=0, padx=2, pady=2, sticky="nsew")
        self.register_widget(btn_add, 'btn_add_directory')
        btn_remove = ttk.Button(btn_frame, text="移除目录", command=self.remove_directory)
        btn_remove.grid(row=0, column=1, padx=2, pady=2, sticky="nsew")
        btn_clear = ttk.Button(btn_frame, text="清空目录", command=self.clear_directories)
        btn_clear.grid(row=0, column=2, padx=2, pady=2, sticky="nsew")
        btn_scan = ttk.Button(btn_frame, text="扫描目录", command=self.scan_selected_directories)
        btn_scan.grid(row=0, column=3, padx=2, pady=2, sticky="nsew")
        btn_refresh = ttk.Button(btn_frame, text="刷新目录", command=self.refresh_directories)
        btn_refresh.grid(row=0, column=4, padx=2, pady=2, sticky="nsew")
        # 设置每列权重为1，实现均匀分布
        for i in range(5):
            btn_frame.columnconfigure(i, weight=1)

        # MongoDB管理section
        self.mongodb_frame = ttk.LabelFrame(self.left_frame, text="MongoDB管理", padding=10)
        self.mongodb_frame.pack(fill=tk.X, padx=10, pady=(5, 10))
        self.register_widget(self.mongodb_frame, 'mongodb_frame')

        # 新增：MongoDB连接状态Label和操作状态Label并排显示
        status_labels_frame = ttk.Frame(self.mongodb_frame)
        status_labels_frame.pack(fill=tk.X, padx=0, pady=0)
        self.register_widget(status_labels_frame, 'status_labels_frame')

        self.mongodb_status_var = tk.StringVar(value="连接状态: 未连接")
        self.mongodb_status_label = ttk.Label(status_labels_frame, textvariable=self.mongodb_status_var, foreground="red")
        self.mongodb_status_label.pack(side=tk.LEFT, padx=5, pady=5)
        self.register_widget(self.mongodb_status_label, 'mongodb_status_label')

        self.operation_status_label = ttk.Label(status_labels_frame, text="操作状态: 就绪")
        self.operation_status_label.pack(side=tk.LEFT, padx=10, pady=5)
        self.register_widget(self.operation_status_label, 'operation_status_label')

        # 只保留btns_frame那组按钮
        btn_texts_cmds = [
            ("初始化DB", self.init_mongodb),
            ("读取数据", self.load_database_async),
            ("刷新数据", self.refresh_database),
            ("清空数据", self.clear_database)
        ]
        btns_frame = ttk.Frame(self.mongodb_frame)
        btns_frame.pack(fill=tk.X, padx=0, pady=0)
        self.register_widget(btns_frame, 'btns_frame')
        for i, (text, cmd) in enumerate(btn_texts_cmds):
            btn = ttk.Button(btns_frame, text=text, command=cmd)
            btn.grid(row=0, column=i, padx=5, pady=5, sticky="nsew")
            self.register_widget(btn, f'btn_mongodb_{i}')
        for i in range(len(btn_texts_cmds)):
            btns_frame.columnconfigure(i, weight=1)

        # 3. 规则管理器section
        self.rules_frame = ttk.LabelFrame(self.left_frame, text="规则管理器", padding=10)
        self.rules_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))
        self.register_widget(self.rules_frame, 'rules_frame')
        # 规则管理器内容可后续补充

        # 右侧文件树
        self.file_tree_frame = ttk.LabelFrame(self.right_frame, text="文件树", padding=10)
        self.file_tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.register_widget(self.file_tree_frame, 'file_tree_frame')

        # 新增文件统计面板
        self.file_panel_frame = ttk.Frame(self.file_tree_frame)
        self.file_panel_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        self.register_widget(self.file_panel_frame, 'file_panel_frame')

        # 删除lbl_file_count、lbl_video_count、lbl_junk_count、lbl_whitelist_count
        # 原有统计Label已移除

        self.file_tree_panel = FileTreePanel(self.file_tree_frame, self, self.logger)
        if hasattr(self.file_tree_panel, 'frame'):
            frame = getattr(self.file_tree_panel, 'frame')
            frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self.register_widget(frame, 'file_tree_panel_frame')
            
            # 直接为文件树添加滚轮事件绑定
            if hasattr(self.file_tree_panel, 'tree'):
                def _on_right_mousewheel(event):
                    self.file_tree_panel.tree.yview_scroll(int(-1*(event.delta/120)), "units")
                    return "break"
                self.file_tree_panel.tree.bind("<MouseWheel>", _on_right_mousewheel)

    # 接口实现方法
    def create_widgets(self) -> None:
        """创建所有UI控件（重构为controller_frame+information_frame结构，信息区frame只在information_frame下创建）"""
        try:
            # 获取主题背景色，必须在所有用到bg_color的控件创建前
            theme_manager = self.ui_factory.get_theme_manager() if hasattr(self.ui_factory, 'get_theme_manager') else None
            if theme_manager is None:
                from .factory import UIThemeManager
                theme_manager = UIThemeManager()
            theme = theme_manager.get_theme()
            bg_color = theme['colors']['background'] if 'colors' in theme and 'background' in theme['colors'] else '#f0f0f0'
            self.style.configure('Info.TFrame', background=bg_color)

            # 主框架
            if self.main_frame is None:
                self.main_frame = ttk.Frame(self.root)
                self.main_frame.pack(fill=tk.BOTH, expand=True)

            # ========== Information Frame 可调高度 ========== 
            # 用PanedWindow实现可调高度
            self.vertical_paned = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
            self.vertical_paned.pack(fill=tk.BOTH, expand=True)
            # 顶部为主内容区（水平分割）
            self.paned_window = ttk.PanedWindow(self.vertical_paned, orient=tk.HORIZONTAL)

            # ----------- 新增：左侧可滚动Canvas -------------
            # 创建左侧容器Frame来容纳Canvas和滚动条
            self.left_container = ttk.Frame(self.paned_window)
            self.register_widget(self.left_container, 'left_container')
            
            # 创建Canvas和滚动条
            self.left_canvas = tk.Canvas(self.left_container, borderwidth=0, highlightthickness=0)
            self.register_widget(self.left_canvas, 'left_canvas')
            self.left_vscrollbar = ttk.Scrollbar(self.left_container, orient="vertical", command=self.left_canvas.yview)
            self.register_widget(self.left_vscrollbar, 'left_vscrollbar')
            self.left_hscrollbar = ttk.Scrollbar(self.left_container, orient="horizontal", command=self.left_canvas.xview)
            self.register_widget(self.left_hscrollbar, 'left_hscrollbar')
            
            # 布局Canvas和滚动条
            self.left_canvas.grid(row=0, column=0, sticky="nsew")
            self.left_vscrollbar.grid(row=0, column=1, sticky="ns")
            self.left_hscrollbar.grid(row=1, column=0, sticky="ew")
            
            # 配置grid权重
            self.left_container.grid_rowconfigure(0, weight=1)
            self.left_container.grid_columnconfigure(0, weight=1)
            
            # 创建left_frame作为Canvas内容Frame（不指定width）
            self.left_frame = ttk.Frame(self.left_canvas)
            self.register_widget(self.left_frame, 'left_frame')
            self.left_frame_id = self.left_canvas.create_window((0, 0), window=self.left_frame, anchor="nw")
            # 绑定Frame和Canvas的大小变化事件
            def _on_left_frame_configure(event):
                self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
            self.left_frame.bind("<Configure>", _on_left_frame_configure)
            def _on_canvas_configure(event):
                canvas_width = event.width
                self.left_canvas.itemconfig(self.left_frame_id, width=canvas_width)
            self.left_canvas.bind("<Configure>", _on_canvas_configure)
            
            # 鼠标滚轮支持 - 直接绑定到控件
            def _on_mousewheel(event):
                self.left_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            self.left_canvas.bind("<MouseWheel>", _on_mousewheel)
            
            # 横向滚动支持（Shift+滚轮）
            def _on_shift_mousewheel(event):
                self.left_canvas.xview_scroll(int(-1*(event.delta/120)), "units")
            self.left_canvas.bind("<Shift-MouseWheel>", _on_shift_mousewheel)
            
            # ----------- 右侧不变 -------------
            self.right_frame = ttk.Frame(self.paned_window)
            self.register_widget(self.right_frame, 'right_frame')
            self.paned_window.add(self.left_container, weight=1)
            self.paned_window.add(self.right_frame, weight=3)
            self.vertical_paned.add(self.paned_window, weight=4)
            self.information_frame = ttk.Frame(self.vertical_paned, style='Info.TFrame')
            self.vertical_paned.add(self.information_frame, weight=1)
            # 设置底部信息区的最小高度，确保状态栏可见
            self.information_frame.config(height=250)
            # 不要再 pack/pad/propagate information_frame

            # 目录管理、MongoDB、规则管理、文件树等控件全部放到left_frame/right_frame
            self.create_management_panel()  # 只负责left_frame/right_frame内容

            # ========== 信息区frame只在information_frame下创建 ==========
            # stats_frame 相关代码已删除

            # 创建notebook及各功能面板
            self._create_panels()

            # 重要：先创建状态栏，确保它优先占用底部空间
            self._create_status_bar()

            # 然后创建日志框架，占用剩余空间
            self._create_log_frame(bg_color)

            # 设置初始的分割窗口位置，确保底部信息区可见
            self.root.after(100, self._setup_initial_paned_position)

            self.logger.info("[UI组件] 创建完成 - 界面已就绪")

            # 创建完所有UI控件后，主动同步字体
            if hasattr(self, 'config_loader') and self.config_loader:
                try:
                    settings_config = self.config_loader.get_config("settings")
                    if settings_config and 'general' in settings_config:
                        font_size = settings_config['general'].get('font_size', 10)
                    else:
                        font_size = 10
                except Exception:
                    font_size = 10
            else:
                font_size = 10
            if hasattr(self.ui_factory, 'apply_font_size'):
                self.ui_factory.apply_font_size(font_size)

            # 直接绑定滚轮事件到各控件，无需区域进入/离开事件

        except Exception as e:
            self.logger.error(f"创建UI组件失败: {e}")
            raise

    def _create_panels(self) -> None:
        """创建各个功能面板，全部使用 pack 布局，避免 pack/grid 混用"""
        try:
            # 创建规则相关tab (Notebook) 在 rules_frame 下
            self.notebook = ttk.Notebook(self.rules_frame)
            self.register_widget(self.notebook, 'notebook')
            self.notebook.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

            # 使用通用方法创建面板
            self.rename_panel = self._create_panel_with_factory('rename', self.notebook)
            if self.rename_panel:
                self.notebook.add(self.rename_panel.get_frame(), text="重命名")

            self.duplicate_panel = self._create_panel_with_factory('duplicate', self.notebook)
            if self.duplicate_panel:
                self.notebook.add(self.duplicate_panel.get_frame(), text="重复文件")

            # 使用通用方法创建其他面板
            self.junk_panel = self._create_panel_with_factory('junk', self.notebook)
            if self.junk_panel:
                self.notebook.add(self.junk_panel.get_frame(), text="垃圾文件")

            self.whitelist_panel = self._create_panel_with_factory('whitelist', self.notebook)
            if self.whitelist_panel:
                self.notebook.add(self.whitelist_panel.get_frame(), text="白名单")

            self.settings_panel = self._create_panel_with_factory('settings', self.notebook)
            if self.settings_panel:
                self.notebook.add(self.settings_panel.get_frame(), text="设置")

            # 确保所有面板都正确显示
            self.notebook.select(0)
            self.notebook.update()

            self.logger.info("所有面板创建成功")

        except Exception as e:
            self.logger.error(f"创建面板失败: {e}")
            raise
    
    def _create_status_bar(self) -> None:
        """
        创建状态栏（高度固定），使用place几何管理器绝对定位在底部
        状态栏高度固定为60px，位置固定，不受底部信息区大小调整影响
        """
        self.status_bar = StatusBar(self.information_frame)
        frame = self.status_bar.frame

        # 使用place几何管理器绝对定位状态栏在底部
        frame.place(relx=0, rely=1.0, relwidth=1.0, height=60, anchor='sw')
        frame.config(height=60)  # 固定高度60像素
        frame.pack_propagate(False)  # 防止内容改变frame大小

        # 设置中止按钮的回调函数
        self.status_bar.set_stop_callback(self.stop_current_task)

    def _create_log_frame(self, bg_color) -> None:
        """
        创建日志框架和任务概览面板的水平分割布局，为状态栏预留60像素底部空间
        """
        # 创建主容器，占用除状态栏外的所有剩余空间（底部预留60px给状态栏）
        main_container = ttk.Frame(self.information_frame)
        main_container.pack(side=tk.TOP, fill=tk.BOTH, expand=True, pady=(2, 62))

        # 创建水平分割窗口（日志在左，任务概览在右）
        self.log_task_paned = HorizontalPanedWindow(
            main_container,
            initial_ratio=0.7,  # 日志窗口占70%，任务概览占30%
            sash_width=6
        )
        self.log_task_paned.pack(fill=tk.BOTH, expand=True)

        # 设置最小尺寸
        self.log_task_paned.set_minimum_sizes(left_min=300, right_min=250)

        # 获取左右面板
        log_container = self.log_task_paned.get_left_frame()
        task_container = self.log_task_paned.get_right_frame()

        # 创建日志框架（左侧面板）
        self.log_frame = ttk.LabelFrame(log_container, text="日志输出", padding=10, style='Log.TLabelframe')
        self.log_frame.pack(fill=tk.BOTH, expand=True)
        self.style.configure('Log.TLabelframe', background=bg_color)
        self.log_frame.configure(style='Log.TLabelframe')

        # 创建日志文本框（恢复原来的高度）
        self.log_text = tk.Text(self.log_frame, wrap="word", state="disabled", height=8, bg=bg_color)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        self.register_widget(self.log_text, 'log_text')

        # 日志滚动条
        log_vsb = ttk.Scrollbar(self.log_frame, orient="vertical", command=self.log_text.yview)
        log_vsb.pack(side=tk.RIGHT, fill=tk.Y, padx=2, pady=2)
        self.log_text.config(yscrollcommand=log_vsb.set)
        self.log_scrollbar = log_vsb
        self.register_widget(log_vsb, 'log_scrollbar')

        # 创建任务概览面板（右侧面板）
        label_font_str = self.style.lookup("TLabel", "font")
        font_tuple = self._safe_parse_font(label_font_str)

        self.task_overview_panel = TaskOverviewPanel(task_container, font=font_tuple)
        task_frame = self.task_overview_panel.get_frame()
        task_frame.pack(fill=tk.BOTH, expand=True)

    def _setup_initial_paned_position(self) -> None:
        """设置初始的分割窗口位置，确保底部信息区可见"""
        try:
            # 获取窗口总高度
            total_height = self.root.winfo_height()
            if total_height > 100:  # 确保窗口已经正确显示
                # 设置底部信息区占总高度的30%，但至少250像素，最多400像素
                bottom_height = max(250, min(400, int(total_height * 0.30)))
                main_height = total_height - bottom_height

                # 设置垂直分割窗口的位置
                self.vertical_paned.sashpos(0, main_height)
                self.logger.info(f"设置初始分割位置: 主区域={main_height}px, 底部区域={bottom_height}px")
            else:
                # 如果窗口还没有正确显示，延迟重试
                self.root.after(100, self._setup_initial_paned_position)
        except Exception as e:
            self.logger.warning(f"设置初始分割位置失败: {e}")
            # 使用备用方案：直接设置像素位置
            try:
                self.vertical_paned.sashpos(0, 500)  # 假设主区域500像素
            except Exception:
                pass

    def bind_events(self) -> None:
        """绑定事件"""
        try:
            # 绑定用户活动事件
            self.root.bind('<Button-1>', self._on_user_activity)
            self.root.bind('<Key>', self._on_user_activity)
            self.root.bind('<Motion>', self._on_user_activity)
            
            # 绑定其他事件...
            self.root.protocol("WM_DELETE_WINDOW", self.on_close)
            
            # 使用通用方法绑定所有面板事件
            self._bind_all_panel_events()

            self.logger.debug("事件绑定完成")
            
        except Exception as e:
            self.logger.error(f"绑定事件失败: {e}")
    
    def _on_user_activity(self, event=None):
        """用户活动检测"""
        try:
            # 更新数据库状态监控器的用户活动时间
            if hasattr(self, 'db_status_monitor') and self.db_status_monitor:
                self.db_status_monitor.update_user_activity()
        except Exception as e:
            self.logger.error(f"处理用户活动失败: {e}")
    
    def update_ui(self, data: Dict[str, Any]) -> None:
        """更新UI显示 - 重构版本"""
        try:
            # 使用通用方法更新所有面板
            self._update_all_panels(data)

            # 更新状态栏
            if self.status_bar:
                if "status" in data:
                    self.status_bar.show_message(data["status"])
                if "progress" in data:
                    self.status_bar.update_progress(data["progress"])

        except Exception as e:
            self.logger.error(f"更新UI失败: {e}")

    # ==================== UI重构辅助方法 ====================

    def _safe_panel_operation(self, panel_name: str, operation: str, *args, **kwargs) -> bool:
        """
        安全执行面板操作的通用方法

        参数:
            panel_name: 面板名称
            operation: 操作名称
            *args, **kwargs: 操作参数

        返回:
            bool: 操作是否成功
        """
        try:
            panel = getattr(self, panel_name, None)
            if panel and hasattr(panel, operation):
                method = getattr(panel, operation)
                if callable(method):
                    method(*args, **kwargs)
                    return True
            return False
        except Exception as e:
            self.logger.error(f"面板操作失败 - {panel_name}.{operation}: {e}")
            return False

    def _update_all_panels(self, data: Dict[str, Any]) -> None:
        """
        更新所有面板的通用方法

        参数:
            data: 更新数据
        """
        panel_names = [
            'file_tree_panel', 'rename_panel', 'duplicate_panel',
            'junk_panel', 'whitelist_panel', 'settings_panel'
        ]

        for panel_name in panel_names:
            self._safe_panel_operation(panel_name, 'update_ui', data)

    def _bind_all_panel_events(self) -> None:
        """
        绑定所有面板事件的通用方法
        """
        panel_names = [
            'duplicate_panel', 'junk_panel', 'whitelist_panel',
            'rename_panel', 'settings_panel'
        ]

        for panel_name in panel_names:
            self._safe_panel_operation(panel_name, 'bind_events')

    def _refresh_all_panels(self) -> None:
        """
        刷新所有面板的通用方法
        """
        panel_names = [
            'file_tree_panel', 'rename_panel', 'duplicate_panel',
            'junk_panel', 'whitelist_panel', 'settings_panel'
        ]

        for panel_name in panel_names:
            self._safe_panel_operation(panel_name, 'refresh')

    def _create_panel_with_factory(self, panel_type: str, parent: tk.Widget) -> Any:
        """
        使用工厂创建面板的通用方法

        参数:
            panel_type: 面板类型
            parent: 父组件

        返回:
            创建的面板对象
        """
        try:
            factory_method = getattr(self.ui_factory, f'create_{panel_type}_panel', None)
            if factory_method and callable(factory_method):
                panel = factory_method(parent, self)
                if panel and hasattr(panel, 'get_frame'):
                    return panel
                else:
                    self.logger.error(f"面板创建失败或缺少get_frame方法: {panel_type}")
            else:
                self.logger.error(f"工厂方法不存在: create_{panel_type}_panel")
        except Exception as e:
            self.logger.error(f"创建面板失败 - {panel_type}: {e}")
        return None

    def _safe_ui_update(self, update_func: Callable, error_context: str = "UI更新") -> None:
        """
        安全执行UI更新的通用方法

        参数:
            update_func: 更新函数
            error_context: 错误上下文描述
        """
        try:
            if callable(update_func):
                self.root.after(0, update_func)
        except Exception as e:
            self.logger.error(f"{error_context}失败: {e}")

    def _get_unified_duplicate_finder(self) -> DuplicateFinder:
        """
        获取统一的重复文件查找器

        返回:
            DuplicateFinder: 重复文件查找器实例
        """
        try:
            return DuplicateFinder(
                db_manager=self.db_manager,
                use_database=True
            )
        except Exception as e:
            self.logger.error(f"创建重复文件查找器失败: {e}")
            return DuplicateFinder(use_database=False)

    def get_frame(self) -> Optional[ttk.Frame]:
        """获取主框架"""
        return self.main_frame
    
    def destroy(self) -> None:
        """销毁组件"""
        if not self._destroyed:
            try:
                self.logger.info("开始销毁UI组件...")
                
                # 先取消所有事件订阅
                if hasattr(self, 'event_system') and self.event_system:
                    try:
                        self.event_system.unsubscribe_all("main_window")
                        self.logger.info("已取消所有事件订阅")
                    except Exception as e:
                        self.logger.error(f"取消事件订阅失败: {e}")
                
                # 销毁所有面板
                panels = [
                    self.file_tree_panel, self.rename_panel, self.duplicate_panel,
                    self.junk_panel, self.whitelist_panel, self.settings_panel
                ]
                for panel in panels:
                    if panel and hasattr(panel, 'destroy'):
                        try:
                            panel_name = panel.__class__.__name__ if hasattr(panel, '__class__') else "未知面板"
                            self.logger.debug(f"正在销毁面板: {panel_name}")
                            
                            # 检查面板的frame是否存在
                            if hasattr(panel, 'frame'):
                                panel_frame = getattr(panel, 'frame')
                                if panel_frame and hasattr(panel_frame, 'winfo_exists'):
                                    try:
                                        if panel_frame.winfo_exists():
                                            panel.destroy()
                                    except Exception:
                                        # 如果winfo_exists调用失败，直接尝试销毁
                                        panel.destroy()
                                else:
                                    panel.destroy()
                            else:
                                panel.destroy()
                                
                            self.logger.debug(f"面板已销毁: {panel_name}")
                        except Exception as e:
                            self.logger.error(f"销毁面板失败: {e}")
                
                # 销毁状态栏
                if self.status_bar:
                    try:
                        self.logger.debug("正在销毁状态栏...")
                        if hasattr(self.status_bar, 'destroy'):
                            self.status_bar.destroy()
                        elif hasattr(self.status_bar, 'frame'):
                            status_frame = getattr(self.status_bar, 'frame')
                            if status_frame and hasattr(status_frame, 'destroy'):
                                status_frame.destroy()
                        self.logger.debug("状态栏已销毁")
                    except Exception as e:
                        self.logger.error(f"销毁状态栏失败: {e}")
                
                # 销毁主框架
                if self.main_frame:
                    try:
                        self.logger.debug("正在销毁主框架...")
                        if hasattr(self.main_frame, 'winfo_exists'):
                            try:
                                if self.main_frame.winfo_exists():
                                    self.main_frame.destroy()
                            except Exception:
                                # 如果winfo_exists调用失败，直接尝试销毁
                                self.main_frame.destroy()
                        else:
                            self.main_frame.destroy()
                        self.logger.debug("主框架已销毁")
                    except Exception as e:
                        self.logger.error(f"销毁主框架失败: {e}")
                
                # 清空引用
                self.file_tree_panel = None
                self.rename_panel = None
                self.duplicate_panel = None
                self.junk_panel = None
                self.whitelist_panel = None
                self.settings_panel = None
                self.status_bar = None
                self.main_frame = None
                
                self._destroyed = True
                self.logger.info("所有UI组件已销毁")
            except Exception as e:
                self.logger.error(f"销毁主窗口失败: {e}")
                self.logger.error(f"错误详情: {traceback.format_exc()}")
    
    def handle_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理事件"""
        try:
            if event_type == "file_scanned":
                self.on_file_scanned(data)
            elif event_type == "scan_completed":
                self.on_scan_completed(data)
            elif event_type == "db_connected":
                self.on_db_connected(data)
            elif event_type == "db_disconnected":
                self.on_db_disconnected(data)
            elif event_type == "config_changed":
                self.on_config_changed(data)
            else:
                self.logger.debug(f"未处理的事件类型: {event_type}")
                
        except Exception as e:
            self.logger.error(f"处理事件失败 {event_type}: {e}")
    
    def subscribe_events(self) -> None:
        """订阅事件系统事件"""
        # 先取消所有旧订阅
        if hasattr(self, 'event_system') and self.event_system:
            self.event_system.unsubscribe_all("main_window")
        # 订阅新事件
        if hasattr(self, 'event_system') and self.event_system:
            self.event_system.subscribe("file_scanned", self.on_file_scanned, subscriber_id="main_window")
            self.event_system.subscribe("file_renamed", self.on_file_renamed, subscriber_id="main_window")
            self.event_system.subscribe("file_deleted", self.on_file_deleted, subscriber_id="main_window")
            self.event_system.subscribe("file_moved", self.on_file_moved, subscriber_id="main_window")
            self.event_system.subscribe("scan_completed", self.on_scan_completed, subscriber_id="main_window")
            self.event_system.subscribe("db_connected", self.on_db_connected, subscriber_id="main_window")
            self.event_system.subscribe("db_disconnected", self.on_db_disconnected, subscriber_id="main_window")
            self.event_system.subscribe("db_operation_start", self.on_db_operation_start, subscriber_id="main_window")
            self.event_system.subscribe("db_operation_complete", self.on_db_operation_complete, subscriber_id="main_window")
            self.event_system.subscribe("db_operation_error", self.on_db_operation_error, subscriber_id="main_window")
            self.event_system.subscribe("db_stats_updated", self.on_db_stats_updated, subscriber_id="main_window")
    
    def unsubscribe_events(self) -> None:
        """取消订阅事件"""
        # 取消所有事件订阅
        if hasattr(self, 'event_system') and self.event_system:
            self.event_system.unsubscribe_all("main_window")
    
    def show_message(self, message: str, duration: int = 0) -> None:
        """显示消息"""
        if self.status_bar:
            self.status_bar.show_message(message, duration)
    
    def update_progress(self, progress: float, message: str = "") -> None:
        """更新进度"""
        try:
            # 检查状态栏是否存在
            if not hasattr(self, 'status_bar') or not self.status_bar:
                self.logger.warning("状态栏不存在，无法更新进度")
                return

            # 检查进度值范围
            progress = max(0, min(100, progress))

            # 更新进度条
            if hasattr(self.status_bar, 'update_progress'):
                self.status_bar.update_progress(progress, message)
                self.logger.debug(f"进度更新: {progress}% - {message}")
            else:
                self.logger.warning("状态栏缺少update_progress方法")

        except Exception as e:
            self.logger.error(f"更新进度失败: {e}")
            print(f"更新进度失败: {e}")
    
    def clear_status(self) -> None:
        """清除状态"""
        if self.status_bar:
            self.status_bar.clear_status()
    
    def update_connection_status(self, is_connected: bool) -> None:
        """更新数据库连接状态"""
        if self.status_bar:
            self.status_bar.update_connection_status(is_connected)

    def update_file_stats(self, stats: Dict[str, Any]) -> None:
        """更新文件统计信息"""
        if self.status_bar:
            if hasattr(self.status_bar, 'update_file_stats'):
                self.status_bar.update_file_stats(stats)

    def update_all(self, progress: float = 0, message: str = "", is_connected: bool = False, stats: Dict[str, Any] = {}) -> None:
        """更新所有状态信息"""
        if self.status_bar:
            if hasattr(self.status_bar, 'update_all'):
                self.status_bar.update_all(progress, message)

    def apply_theme(self, theme: Dict[str, Any]) -> None:
        """应用主题"""
        self._theme = theme
        
        # 应用主题到UI工厂
        theme_manager = self.ui_factory.get_theme_manager() if hasattr(self.ui_factory, 'get_theme_manager') else None
        if theme_manager and hasattr(theme_manager, 'set_theme'):
            theme_manager.set_theme(theme)
        
        # 重新应用样式
        style = ttk.Style()
        if theme_manager and hasattr(theme_manager, 'apply_theme_to_style'):
            theme_manager.apply_theme_to_style(style)
        
        # 通知所有面板应用主题
        panels = [
            self.file_tree_panel, self.rename_panel, self.duplicate_panel,
            self.junk_panel, self.whitelist_panel, self.settings_panel
        ]
        
        for panel in panels:
            if panel and hasattr(panel, 'apply_theme'):
                panel.apply_theme(theme)
    
    def get_current_theme(self) -> Dict[str, Any]:
        """获取当前主题"""
        return self._theme.copy()
    
    def show(self) -> None:
        """显示窗口"""
        try:
            # 确保所有组件都已创建
            if not self.main_frame or not self.notebook:
                raise ValueError("UI组件未完全初始化")
            # 显示窗口
            self.root.deiconify()
            self.root.lift()
            # 强制更新布局
            self.main_frame.update_idletasks()
            self.root.update_idletasks()
            # 将窗口移到屏幕中央
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            window_width = self.root.winfo_width()
            window_height = self.root.winfo_height()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            self.root.geometry(f"+{x}+{y}")
            self.logger.info("窗口显示成功")
        except Exception as e:
            self.logger.error(f"显示窗口失败: {e}")
            raise
    
    def hide(self) -> None:
        """隐藏窗口"""
        self.root.withdraw()
    
    def close(self) -> None:
        """关闭窗口"""
        self.on_close()
    
    def get_root(self) -> tk.Tk:
        """获取根窗口"""
        return self.root
    
    def refresh_all(self) -> None:
        """刷新所有面板 - 重构版本"""
        self._refresh_all_panels()
    
    def on_config_changed(self, data: Dict[str, Any]) -> None:
        """配置变更事件处理"""
        try:
            config = data.get("config", {})
            
            # 应用主题配置
            theme_config = config.get("theme", {})
            if theme_config:
                # 这里可以根据配置更新主题
                pass
            
            # 通知所有面板配置已变更
            self.update_ui({"config": config})
            
            self.logger.info("配置已更新")
            
        except Exception as e:
            self.logger.error(f"处理配置变更失败: {e}")
    
    def on_close(self):
        """处理窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            # 中止当前任务
            if self.current_task and self.task_running.is_set():
                self.stop_current_task()
                # 等待任务中止，最多等待1秒
                for _ in range(10):
                    if not self.task_running.is_set():
                        break
                    time.sleep(0.1)
            
            # 停止数据库状态监控器（必须在其他清理之前）
            if hasattr(self, 'db_status_monitor') and self.db_status_monitor:
                self.logger.info("正在停止数据库状态监控器...")
                try:
                    # 停止hash计算
                    if hasattr(self.db_status_monitor, 'hash_calculation_running') and self.db_status_monitor.hash_calculation_running:
                        self.db_status_monitor.stop_hash_calculation()
                    # 停止监控
                    self.db_status_monitor.stop_monitoring()
                    self.logger.info("数据库状态监控器已停止")
                except Exception as e:
                    self.logger.error(f"停止数据库状态监控器失败: {e}")

            # 停止工作线程
            self.task_queue.put(None)
            if hasattr(self, 'worker_thread'):
                self.worker_thread.join(timeout=1.0)  # 最多等待1秒

            # 取消事件订阅
            if hasattr(self, 'event_system') and self.event_system:
                self.event_system.unsubscribe_all("main_window")

            # 关闭异步管理器
            if hasattr(self, 'async_manager') and self.async_manager:
                self.async_manager.shutdown()
            
            # 如果工作线程仍在运行，强制退出
            if hasattr(self, 'worker_thread') and self.worker_thread.is_alive():
                self.logger.warning("工作线程未能正常退出，强制关闭程序")
            
            # 先销毁UI组件，避免在root.destroy()后访问已销毁的窗口
            try:
                # 先清理所有面板
                self.destroy()
                self.logger.info("UI组件已清理")
            except Exception as e:
                self.logger.error(f"清理UI组件失败: {e}")
            
            # 最后销毁根窗口
            try:
                self.root.destroy()
                self.logger.info("根窗口已销毁")
            except Exception as e:
                self.logger.error(f"清理根窗口失败: {e}")

            # 强制退出程序，确保所有线程都被终止
            import sys
            import os
            self.logger.info("强制退出程序，确保所有后台线程终止")
            try:
                # 给其他清理操作一点时间
                import time
                time.sleep(0.5)
                # 强制退出
                os._exit(0)
            except Exception as e:
                self.logger.error(f"强制退出失败: {e}")
                sys.exit(0)
    
    def stop_current_task(self):
        """增强的停止当前任务方法"""
        try:
            self.logger.info("用户请求停止当前任务")

            # 使用增强的中断管理器
            interrupt_manager = get_interrupt_manager()

            # 中断所有活跃任务
            active_tasks = interrupt_manager.get_active_tasks()
            if active_tasks:
                self.logger.info(f"发现 {len(active_tasks)} 个活跃任务，正在中断...")
                for task_id in active_tasks:
                    interrupt_manager.request_interrupt(
                        task_id,
                        InterruptReason.USER_REQUESTED,
                        "用户点击中止按钮"
                    )

            # 停止hash值计算
            if hasattr(self, 'db_status_monitor') and self.db_status_monitor:
                if hasattr(self.db_status_monitor, 'hash_calculation_running') and self.db_status_monitor.hash_calculation_running:
                    self.logger.info("停止hash值计算")
                    self.db_status_monitor.stop_hash_calculation()

            # 停止各种进行中的任务
            task_flags = [
                ('scanning_in_progress', '文件扫描'),
                ('duplicate_finding_in_progress', '重复文件查找'),
                ('junk_finding_in_progress', '垃圾文件查找'),
                ('whitelist_finding_in_progress', '白名单文件查找'),
                ('rename_in_progress', '文件重命名'),
                ('delete_in_progress', '文件删除'),
                ('move_in_progress', '文件移动'),
                ('file_tree_loading', '文件树加载')
            ]

            for flag_name, task_name in task_flags:
                if hasattr(self, flag_name) and getattr(self, flag_name):
                    setattr(self, flag_name, False)
                    self.logger.info(f"停止{task_name}")

            # 设置传统中断事件（向后兼容）
            self.interrupt_event.set()
            self.logger.info("已设置主窗口中断事件")

            # 更新状态栏显示
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message("正在中断任务...", 5000)

                # 延迟更新状态
                def delayed_status_update():
                    time.sleep(2)
                    if hasattr(self, 'status_bar') and self.status_bar:
                        self.status_bar.show_message("所有任务已停止", 3000)

                status_thread = threading.Thread(target=delayed_status_update, daemon=True)
                status_thread.start()

            # 同步中断事件到当前任务（关键修复）
            if hasattr(self, '_current_task_interrupt_event') and self._current_task_interrupt_event:
                try:
                    self._current_task_interrupt_event.set()
                    self.logger.info("已同步中断事件到当前任务")
                except Exception as e:
                    self.logger.error(f"同步中断事件失败: {e}")

            # 特别处理：如果有当前任务ID，尝试通过任务管理器中断
            if hasattr(self, 'current_task_id') and self.current_task_id:
                try:
                    from src.utils.unified_task_manager import UnifiedTaskManager
                    manager = UnifiedTaskManager()

                    # 尝试通过任务管理器中断任务
                    if hasattr(manager.async_task_manager, 'interrupt_events'):
                        if self.current_task_id in manager.async_task_manager.interrupt_events:
                            task_interrupt_event = manager.async_task_manager.interrupt_events[self.current_task_id]
                            task_interrupt_event.set()
                            self.logger.info(f"已通过任务管理器中断任务: {self.current_task_id}")
                        else:
                            self.logger.warning(f"任务 {self.current_task_id} 的中断事件未找到")

                    # 清除当前任务ID
                    self.current_task_id = None

                except Exception as e:
                    self.logger.error(f"通过任务管理器中断任务失败: {e}")

            self.logger.info("中断请求处理完成")

            # 取消异步任务（如果存在）
            if self.current_task_id:
                try:
                    from src.utils.unified_task_manager import UnifiedTaskManager
                    manager = UnifiedTaskManager()
                    success = manager.cancel(self.current_task_id)
                    if success:
                        self.logger.info(f"成功取消异步任务: {self.current_task_id}")
                    else:
                        self.logger.warning(f"无法取消异步任务: {self.current_task_id}")
                except Exception as e:
                    self.logger.error(f"取消异步任务失败: {e}")

            # 清除任务运行状态（关键修复）
            self.task_running.clear()
            self.current_task = None
            self.current_task_id = None
            # 注意：不要立即清除中断事件，让异步任务有时间检测到中断信号
            # self.interrupt_event.clear()  # 延迟清除，在任务真正停止后再清除
            self.logger.info("任务运行状态已清除")

            # 重置进度条和状态
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.update_progress(0, "任务已停止")
                self.status_bar.disable_stop_button()
            
            # 重置任务标签
            if hasattr(self, 'task_label') and self.task_label is not None:
                self.task_label.set("当前任务: 无")
            if hasattr(self, 'subtask_label') and self.subtask_label is not None:
                self.subtask_label.set("子任务: 无")
            # 重置进度条
            if hasattr(self, 'progress_var') and self.progress_var is not None:
                self.progress_var.set(0)
            
            # 显示停止消息
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message("任务已停止", 3000)

            # 延迟清除中断事件，给异步任务时间检测中断信号
            def clear_interrupt_event():
                if hasattr(self, 'interrupt_event') and self.interrupt_event:
                    self.interrupt_event.clear()
                    self.logger.info("中断事件已清除")

            # 1秒后清除中断事件
            if hasattr(self, 'root') and self.root:
                self.root.after(1000, clear_interrupt_event)

            self.logger.info("当前任务已停止")
            
        except Exception as e:
            error_msg = f"停止任务失败: {e}"
            self.logger.error(error_msg)
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message(error_msg, 5000)

    def do_scan_directory(self, directories):
        """
        执行目录扫描 - 采用异步回调和主线程调度，防止UI阻塞
        参数:
            directories (str or list): 要扫描的目录路径或路径列表
        """
        if isinstance(directories, str):
            directories = [directories]
        if self.file_scanner is None:
            self.logger.error("file_scanner 未初始化，无法扫描目录")
            return
        self.scanning_in_progress = True
        start_time = time.time()
        dirs_str = '\n- '.join(directories)
        self.result_queue.put({
            "type": "progress",
            "data": {
                "progress": 0,
                "status": f"开始扫描目录:\n- {dirs_str}",
                "task": "扫描文件",
                "subtask": "准备中...",
                "log": f"开始扫描目录:\n- {dirs_str}",
                "log_level": "info"
            }
        })

        # 注册扫描任务到进度管理器
        scan_task_id = f"scan_dirs_{int(time.time() * 1000)}"
        self.progress_manager.register_task(
            task_id=scan_task_id,
            task_type=TaskType.FILE_SCAN,
            task_name=f"扫描目录: {', '.join(map(os.path.basename, directories))}",
            total=len(directories)
        )
        self.progress_manager.start_task(scan_task_id)

        # 定义一个符合新标准的回调函数
        def on_scan_progress(task):
            # 这个回调由progress_manager自动调用
            # 我们可以在这里处理UI更新，但现在TaskOverviewPanel会直接监听
            # 为了兼容旧的逻辑，我们仍然可以把信息放入队列
            try:
                # 导入TaskStatus以避免NameError
                from ..utils.unified_progress_manager import TaskStatus

                # 检查任务是否已完成或失败，避免在结束后还更新
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    return

                # 兼容原有的结果队列机制
                elapsed_time = f"{task.elapsed_time:.2f}s"
                q_item = (task.progress, task.status_message, task.current_item, task.current, task.total, elapsed_time)
                self.result_queue.put(q_item)
            except Exception as e:
                self.logger.error(f"处理扫描进度回调失败: {e}")

        # 将回调注册到特定任务 (如果需要任务特定处理)
        # self.progress_manager.register_callback(scan_task_id, on_scan_progress)

        # 异步执行扫描
        async def scan_task():
            try:
                # 直接使用已初始化的file_scanner实例
                await self.file_scanner.scan_directories_async(
                    directories=directories,
                    task_id=scan_task_id,  # 传递task_id给扫描器
                    interrupt_event=self.interrupt_event,
                    update_database=True
                )
                self.progress_manager.complete_task(scan_task_id, success=True, message="扫描完成")

                # 扫描完成后刷新文件树
                self.logger.info("扫描完成，准备刷新文件树")
                # 使用延迟调用确保数据库操作完全完成
                self.root.after(1000, self._refresh_file_tree_after_scan)
            except asyncio.CancelledError:
                self.logger.warning(f"目录扫描任务 {scan_task_id} 被取消")
                self.progress_manager.cancel_task(scan_task_id, "任务被用户取消")
            except Exception as e:
                self.logger.error(f"目录扫描任务 {scan_task_id} 失败: {e}", exc_info=True)
                self.progress_manager.complete_task(scan_task_id, success=False, message=str(e))

        # 检查是否为网络目录并提供用户提示
        is_network_scan = any(self._is_network_directory(d) for d in directories)
        if is_network_scan:
            self.logger.info(f"检测到网络目录扫描: {directories}")
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 5,
                    "status": "检测到网络目录，使用保守扫描策略...",
                    "task": "扫描文件",
                    "subtask": "网络目录预处理",
                    "log": "网络目录扫描可能需要更长时间，程序可能暂时无响应，请耐心等待",
                    "log_level": "warning"
                }
            })

        # 提交异步扫描任务
        self._submit_scan_task_safely(scan_task(), scan_task_id)

    def _refresh_file_tree_after_scan(self):
        """扫描完成后刷新文件树"""
        try:
            self.logger.info("开始扫描完成后的文件树刷新")
            # 标记扫描已完成
            self.scanning_in_progress = False

            # 刷新文件树
            self.load_all_files_from_database()

            # 更新状态栏
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message("扫描完成，文件树已更新", 3000)

            self.logger.info("扫描完成后的文件树刷新完成")

        except Exception as e:
            self.logger.error(f"扫描完成后刷新文件树失败: {e}")
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message(f"文件树刷新失败: {e}", 5000)

    def _is_network_directory(self, path: str) -> bool:
        """
        检测是否为网络目录 - 修正版本，只检测真正的网络路径

        Args:
            path: 目录路径

        Returns:
            bool: 是否为网络目录
        """
        import platform

        if platform.system() == "Windows":
            # 1. UNC路径检测（\\server\share）
            if path.startswith("\\\\"):
                self.logger.debug(f"检测到UNC网络路径: {path}")
                return True

            # 2. 检查是否为映射的网络驱动器
            if len(path) >= 2 and path[1] == ':':
                drive_letter = path[:2]  # 如 "E:"
                try:
                    import subprocess
                    result = subprocess.run(
                        ["net", "use", drive_letter],
                        capture_output=True,
                        text=True,
                        timeout=1  # 减少超时时间
                    )
                    if result.returncode == 0 and "Remote" in result.stdout:
                        self.logger.debug(f"检测到映射的网络驱动器: {drive_letter}")
                        return True
                except Exception as e:
                    self.logger.debug(f"检查网络驱动器失败: {e}")
                    pass

        # 3. Linux/Unix网络挂载点检测
        elif platform.system() in ["Linux", "Darwin"]:
            # 检查常见的网络挂载点
            network_mount_prefixes = ["/mnt/", "/media/", "/net/", "/Network/"]
            for prefix in network_mount_prefixes:
                if path.startswith(prefix):
                    self.logger.debug(f"检测到可能的网络挂载点: {path}")
                    return True

        # 4. 不再基于文件夹名称或关键词进行检测
        # 移除了对中文关键词的检测，避免误判本地目录

        self.logger.debug(f"确认为本地目录: {path}")
        return False

    def _submit_scan_task_safely(self, scan_task_coro, scan_task_id):
        """安全提交扫描任务"""
        try:
            # 使用asyncio.create_task在事件循环中提交任务
            import asyncio

            # 检查是否有运行的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行的事件循环，创建任务
                task = loop.create_task(self._submit_scan_task_async(scan_task_coro, scan_task_id))
            except RuntimeError:
                # 如果没有运行的事件循环，使用线程池执行
                import threading
                def run_async_task():
                    asyncio.run(self._submit_scan_task_async(scan_task_coro, scan_task_id))

                thread = threading.Thread(target=run_async_task, daemon=True)
                thread.start()

        except Exception as e:
            self.logger.error(f"提交扫描任务失败: {e}")
            self.progress_manager.complete_task(scan_task_id, success=False, message=str(e))

    async def _submit_scan_task_async(self, scan_task_coro, scan_task_id):
        """异步提交扫描任务"""
        try:
            task_id = await self.unified_task_manager.submit(
                scan_task_coro,
                use_coroutine=True,
                task_name=f"scan-{scan_task_id}"
            )
            self.logger.info(f"扫描任务已提交: {task_id}")
        except Exception as e:
            self.logger.error(f"异步提交扫描任务失败: {e}")
            self.progress_manager.complete_task(scan_task_id, success=False, message=str(e))

    def do_find_duplicate_files(self, min_size, extensions):
        """处理查找重复文件任务"""
        self.logger.info(f"开始查找重复文件，最小尺寸: {min_size}, 扩展名: {extensions}")

        task_id = f"find_duplicates_{int(time.time() * 1000)}"
        self.progress_manager.register_task(
            task_id=task_id,
            task_type=TaskType.DUPLICATE_FIND,
            task_name="查找重复文件",
            total=100 # 查重过程复杂，使用百分比进度
        )
        self.progress_manager.start_task(task_id)

        # 异步执行
        async def find_task():
            try:
                # 直接使用已初始化的file_scanner实例
                duplicates = await self.file_scanner.find_duplicate_files_async(
                    task_id=task_id,
                    min_size=min_size,
                    extensions=extensions,
                    interrupt_event=self.interrupt_event
                )
                
                # 更新UI
                if duplicates is not None:
                    self.root.after(0, self.duplicate_files_panel.display_duplicates, duplicates)
                    self.progress_manager.complete_task(task_id, success=True, message=f"找到 {len(duplicates)} 组重复文件")
                else:
                    # 可能是被中断了
                     self.progress_manager.cancel_task(task_id, "任务被中断或未找到结果")

            except asyncio.CancelledError:
                self.logger.warning(f"查找重复文件任务 {task_id} 被取消")
                self.progress_manager.cancel_task(task_id, "任务被用户取消")
            except Exception as e:
                self.logger.error(f"查找重复文件任务 {task_id} 失败: {e}", exc_info=True)
                self.progress_manager.complete_task(task_id, success=False, message=str(e))

        # 提交异步查找任务
        self._submit_find_task_safely(find_task(), task_id)

    def _submit_find_task_safely(self, find_task_coro, task_id):
        """安全提交查找任务"""
        try:
            # 使用asyncio.create_task在事件循环中提交任务
            import asyncio

            # 检查是否有运行的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行的事件循环，创建任务
                task = loop.create_task(self._submit_find_task_async(find_task_coro, task_id))
            except RuntimeError:
                # 如果没有运行的事件循环，使用线程池执行
                import threading
                def run_async_task():
                    asyncio.run(self._submit_find_task_async(find_task_coro, task_id))

                thread = threading.Thread(target=run_async_task, daemon=True)
                thread.start()

        except Exception as e:
            self.logger.error(f"提交查找任务失败: {e}")
            self.progress_manager.complete_task(task_id, success=False, message=str(e))

    async def _submit_find_task_async(self, find_task_coro, task_id):
        """异步提交查找任务"""
        try:
            task_id_result = await self.unified_task_manager.submit(
                find_task_coro,
                use_coroutine=True,
                task_name=f"find-duplicates-{task_id}"
            )
            self.logger.info(f"查找任务已提交: {task_id_result}")
        except Exception as e:
            self.logger.error(f"异步提交查找任务失败: {e}")
            self.progress_manager.complete_task(task_id, success=False, message=str(e))

    def do_find_junk_files(self):
        """处理查找垃圾文件任务"""
        try:
            # 检查 file_scanner 是否初始化
            if self.file_scanner is None:
                self.logger.error("file_scanner 未初始化，无法查找垃圾文件")
                return
            
            # 记录开始时间
            start_time = time.time()
            
            # 添加日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": "开始查找垃圾文件",
                    "task": "查找垃圾文件",
                    "subtask": "准备中...",
                    "log": "开始查找垃圾文件",
                    "log_level": "info"
                }
            })
            
            # 查找垃圾文件
            directories = list(self.dir_listbox.get(0, tk.END)) if self.dir_listbox is not None else []
            junk_files = self.file_scanner.find_junk_files(directories)
            
            # 计算查找耗时
            elapsed_time = time.time() - start_time
            
            # 计算垃圾文件统计信息
            total_files = len(junk_files)
            total_size = sum(getattr(f, 'size', 0) for f in junk_files)
            
            # 按扩展名统计垃圾文件
            ext_stats = {}
            for file_info in junk_files:
                ext = getattr(file_info, 'extension', '').lower()
                if ext not in ext_stats:
                    ext_stats[ext] = 0
                ext_stats[ext] += 1
            
            # 构建扩展名统计信息
            ext_info = ""
            if ext_stats:
                ext_info = "\n文件类型统计:\n"
                for ext, count in sorted(ext_stats.items(), key=lambda x: x[1], reverse=True):
                    ext_info += f"  {ext}: {count} 个文件\n"
            
            # 添加完成日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "垃圾文件查找完成",
                    "task": "查找完成",
                    "subtask": f"找到 {total_files} 个垃圾文件",
                    "log": f"垃圾文件查找完成，找到 {total_files} 个垃圾文件，总大小: {format_size(total_size)}，耗时: {elapsed_time:.2f} 秒{ext_info}",
                    "log_level": "success"
                }
            })
            
            # 将结果添加到结果队列
            self.result_queue.put({
                "type": "junk_files",
                "data": junk_files
            })
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"查找垃圾文件时出错: {error_msg}\n{traceback.format_exc()}")
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": f"查找垃圾文件出错: {error_msg}",
                    "task": "查找出错",
                    "subtask": error_msg,
                    "log": f"查找垃圾文件出错: {error_msg}",
                    "log_level": "error"
                }
            })
            
            # 将空结果放入队列
            self.result_queue.put({
                "type": "junk_files",
                "data": []
            })
    
    def do_find_same_name_videos(self):
        """查找同名视频的实现"""
        pass
    
    
    
    def do_find_whitelist_files(self):
        """执行白名单文件查找"""
        try:
            # 检查 file_scanner 是否初始化
            if self.file_scanner is None:
                self.logger.error("file_scanner 未初始化，无法查找白名单文件")
                return
            
            # 记录开始时间
            start_time = time.time()
            
            # 添加日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": "开始查找白名单文件",
                    "task": "查找白名单文件",
                    "subtask": "准备中...",
                    "log": "开始查找白名单文件",
                    "log_level": "info"
                }
            })
            
            # 查找白名单文件
            directories = list(self.dir_listbox.get(0, tk.END)) if self.dir_listbox is not None else []
            whitelist_files = self.file_scanner.find_whitelist_files(directories)
            
            # 计算查找耗时
            elapsed_time = time.time() - start_time
            
            # 计算白名单文件统计信息
            total_files = len(whitelist_files)
            total_size = sum(getattr(f, 'size', 0) for f in whitelist_files)
            
            # 按扩展名统计白名单文件
            ext_stats = {}
            for file_info in whitelist_files:
                ext = getattr(file_info, 'extension', '').lower()
                if ext not in ext_stats:
                    ext_stats[ext] = 0
                ext_stats[ext] += 1
            
            # 构建扩展名统计信息
            ext_info = ""
            if ext_stats:
                ext_info = "\n文件类型统计:\n"
                for ext, count in sorted(ext_stats.items(), key=lambda x: x[1], reverse=True):
                    ext_info += f"  {ext}: {count} 个文件\n"
            
            # 添加完成日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "白名单文件查找完成",
                    "task": "查找完成",
                    "subtask": f"找到 {total_files} 个白名单文件",
                    "log": f"白名单文件查找完成，找到 {total_files} 个白名单文件，总大小: {format_size(total_size)}，耗时: {elapsed_time:.2f} 秒{ext_info}",
                    "log_level": "success"
                }
            })
            
            # 将结果添加到结果队列
            self.result_queue.put({
                "type": "whitelist_files",
                "data": whitelist_files
            })
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"查找白名单文件时出错: {error_msg}\n{traceback.format_exc()}")
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": f"查找白名单文件出错: {error_msg}",
                    "task": "查找出错",
                    "subtask": error_msg,
                    "log": f"查找白名单文件出错: {error_msg}",
                    "log_level": "error"
                }
            })
            
            # 将空结果放入队列
            self.result_queue.put({
                "type": "whitelist_files",
                "data": []
            })
    
    def do_apply_rename_rules(self, data):
        """应用重命名规则的实现，合并所有分支，统一路径参数"""
        if not data or "files" not in data:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "重命名失败: 没有指定要重命名的文件",
                    "task": "重命名失败",
                    "subtask": "参数错误",
                    "log": "应用重命名规则失败: 没有指定要重命名的文件",
                    "log_level": "error"
                }
            })
            return
        files_to_rename = data["files"]
        if not files_to_rename:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "重命名完成: 没有文件需要重命名",
                    "task": "重命名完成",
                    "subtask": "无文件",
                    "log": "没有文件需要重命名",
                    "log_level": "info"
                }
            })
            return
        total_files = len(files_to_rename)
        renamed_count = 0
        failed_count = 0
        failed_files = []
        try:
            for i, file_data in enumerate(files_to_rename):
                if not self.task_running.is_set():
                    self.result_queue.put({
                        "type": "progress",
                        "data": {
                            "progress": 100,
                            "status": "重命名已中止",
                            "task": "已中止",
                            "subtask": f"已重命名 {renamed_count} 个文件",
                            "log": f"文件重命名已被用户中止，已重命名 {renamed_count} 个文件",
                            "log_level": "warning"
                        }
                    })
                    return
                progress = (i / total_files) * 100
                file_path = _normalize_path(file_data.get("file_path") or file_data.get("original_path") or file_data.get("old_path") or file_data.get("path", ""))
                new_file_path = _normalize_path(file_data.get("new_file_path") or file_data.get("new_path") or "")
                if not file_path or not new_file_path:
                    failed_count += 1
                    failed_files.append(f"{file_path} (路径信息不完整)")
                    continue
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": progress,
                        "status": f"正在重命名文件... {i+1}/{total_files}",
                        "task": "重命名文件",
                        "subtask": f"重命名: {os.path.basename(file_path)}",
                        "log": f"正在重命名文件: {file_path} -> {new_file_path}"
                    }
                })
                try:
                    if not os.path.exists(file_path):
                        failed_count += 1
                        failed_files.append(f"{file_path} (源文件不存在)")
                        self.logger.warning(f"源文件不存在，跳过重命名: {file_path}")
                        continue
                    if os.path.exists(new_file_path) and file_path != new_file_path:
                        failed_count += 1
                        failed_files.append(f"{file_path} (目标文件已存在)")
                        self.logger.warning(f"目标文件已存在，跳过重命名: {new_file_path}")
                        continue
                    if file_path == new_file_path:
                        self.logger.info(f"文件名未改变，跳过: {file_path}")
                        continue
                    os.rename(file_path, new_file_path)
                    renamed_count += 1
                    self.logger.info(f"已重命名文件: {file_path} -> {new_file_path}")
                    self.event_system.publish("file_renamed", {
                        "file_path": file_path,
                        "new_file_path": new_file_path,
                        "success": True
                    })
                except Exception as e:
                    failed_count += 1
                    failed_files.append(f"{file_path} ({str(e)})")
                    self.logger.error(f"重命名文件失败: {file_path} -> {new_file_path}, 错误: {str(e)}")
                    self.event_system.publish("file_renamed", {
                        "file_path": file_path,
                        "new_file_path": new_file_path,
                        "success": False,
                        "error": str(e)
                    })
            success_msg = f"重命名完成: 成功重命名 {renamed_count} 个文件"
            if failed_count > 0:
                success_msg += f"，失败 {failed_count} 个文件"
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": success_msg,
                    "task": "重命名完成",
                    "subtask": f"成功: {renamed_count}, 失败: {failed_count}",
                    "log": success_msg,
                    "log_level": "success" if failed_count == 0 else "warning"
                }
            })
            result_message = success_msg
            if failed_files:
                result_message += "\n\n失败的文件:\n" + "\n".join(failed_files[:10])
                if len(failed_files) > 10:
                    result_message += f"\n... 还有 {len(failed_files) - 10} 个文件重命名失败"
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "apply_rename_rules",
                    "success": failed_count == 0,
                    "message": result_message,
                    "renamed_count": renamed_count,
                    "failed_count": failed_count,
                    "failed_files": failed_files
                }
            })
        except Exception as e:
            error_msg = f"应用重命名规则时出错: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": error_msg,
                    "task": "重命名出错",
                    "subtask": str(e),
                    "log": f"{error_msg}\n{traceback.format_exc()}",
                    "log_level": "error"
                }
            })
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "apply_rename_rules",
                    "success": False,
                    "message": error_msg
                }
            })
    
    def do_delete_files(self, data):
        """删除文件的实现"""
        if not data or "files" not in data:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "删除失败: 没有指定要删除的文件",
                    "task": "删除失败",
                    "subtask": "参数错误",
                    "log": "删除文件失败: 没有指定要删除的文件",
                    "log_level": "error"
                }
            })
            return
        
        files_to_delete = data["files"]
        if not files_to_delete:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "删除完成: 没有文件需要删除",
                    "task": "删除完成",
                    "subtask": "无文件",
                    "log": "没有文件需要删除",
                    "log_level": "info"
                }
            })
            return
        
        total_files = len(files_to_delete)
        deleted_count = 0
        failed_count = 0
        failed_files = []
        
        try:
            for i, file_path in enumerate(files_to_delete):
                # 检查任务是否被中止
                if not self.task_running.is_set():
                    self.result_queue.put({
                        "type": "progress",
                        "data": {
                            "progress": 100,
                            "status": "删除已中止",
                            "task": "已中止",
                            "subtask": f"已删除 {deleted_count} 个文件",
                            "log": f"文件删除已被用户中止，已删除 {deleted_count} 个文件",
                            "log_level": "warning"
                        }
                    })
                    return
                
                # 计算进度
                progress = (i / total_files) * 100
                
                # 更新进度
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": progress,
                        "status": f"正在删除文件... {i+1}/{total_files}",
                        "task": "删除文件",
                        "subtask": f"删除: {os.path.basename(file_path)}",
                        "log": f"正在删除文件: {file_path}"
                    }
                })
                
                try:
                    # 删除文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                        self.logger.info(f"已删除文件: {file_path}")
                        
                        # 发布文件删除事件
                        self.event_system.publish("file_deleted", {
                            "file_path": file_path,
                            "success": True
                        })
                    else:
                        self.logger.warning(f"文件不存在，跳过删除: {file_path}")
                        failed_count += 1
                        failed_files.append(f"{file_path} (文件不存在)")
                        
                except Exception as e:
                    failed_count += 1
                    failed_files.append(f"{file_path} ({str(e)})")
                    self.logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                    
                    # 发布文件删除失败事件
                    self.event_system.publish("file_deleted", {
                        "file_path": file_path,
                        "success": False,
                        "error": str(e)
                    })
            
            # 完成删除操作
            success_msg = f"删除完成: 成功删除 {deleted_count} 个文件"
            if failed_count > 0:
                success_msg += f"，失败 {failed_count} 个文件"
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": success_msg,
                    "task": "删除完成",
                    "subtask": f"成功: {deleted_count}, 失败: {failed_count}",
                    "log": success_msg,
                    "log_level": "success" if failed_count == 0 else "warning"
                }
            })
            
            # 发送操作完成结果
            result_message = success_msg
            if failed_files:
                result_message += "\n\n失败的文件:\n" + "\n".join(failed_files[:10])  # 只显示前10个失败文件
                if len(failed_files) > 10:
                    result_message += f"\n... 还有 {len(failed_files) - 10} 个文件删除失败"
            
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "delete_files",
                    "success": failed_count == 0,
                    "message": result_message,
                    "deleted_count": deleted_count,
                    "failed_count": failed_count,
                    "failed_files": failed_files
                }
            })
            
        except Exception as e:
            error_msg = f"删除文件时出错: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": error_msg,
                    "task": "删除出错",
                    "subtask": str(e),
                    "log": f"{error_msg}\n{traceback.format_exc()}",
                    "log_level": "error"
                }
            })
            
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "delete_files",
                    "success": False,
                    "message": error_msg
                }
            })
    
    def do_move_files(self, data):
        """移动文件的实现，合并所有分支，统一路径参数"""
        if not data or "files" not in data:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "移动失败: 没有指定要移动的文件",
                    "task": "移动失败",
                    "subtask": "参数错误",
                    "log": "移动文件失败: 没有指定要移动的文件",
                    "log_level": "error"
                }
            })
            return
        files_to_move = data["files"]
        target_dir = _normalize_path(data.get("target_file_path") or data.get("target_dir") or data.get("target_path") or "")
        if not files_to_move:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "移动完成: 没有文件需要移动",
                    "task": "移动完成",
                    "subtask": "无文件",
                    "log": "没有文件需要移动",
                    "log_level": "info"
                }
            })
            return
        if not target_dir:
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "移动失败: 没有指定目标目录",
                    "task": "移动失败",
                    "subtask": "参数错误",
                    "log": "移动文件失败: 没有指定目标目录",
                    "log_level": "error"
                }
            })
            return
        total_files = len(files_to_move)
        moved_count = 0
        failed_count = 0
        failed_files = []
        try:
            for i, file_data in enumerate(files_to_move):
                if not self.task_running.is_set():
                    self.result_queue.put({
                        "type": "progress",
                        "data": {
                            "progress": 100,
                            "status": "移动已中止",
                            "task": "已中止",
                            "subtask": f"已移动 {moved_count} 个文件",
                            "log": f"文件移动已被用户中止，已移动 {moved_count} 个文件",
                            "log_level": "warning"
                        }
                    })
                    return
                progress = (i / total_files) * 100
                file_path = _normalize_path(file_data.get("file_path") or file_data.get("source_path") or file_data.get("path") or str(file_data))
                if not file_path:
                    failed_count += 1
                    failed_files.append(f"未知路径 (路径信息不完整)")
                    continue
                file_name = os.path.basename(file_path)
                target_path = os.path.join(target_dir, file_name)
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": progress,
                        "status": f"正在移动文件... {i+1}/{total_files}",
                        "task": "移动文件",
                        "subtask": f"移动: {file_name}",
                        "log": f"正在移动文件: {file_path} -> {target_path}"
                    }
                })
                try:
                    if not os.path.exists(file_path):
                        failed_count += 1
                        failed_files.append(f"{file_path} (源文件不存在)")
                        self.logger.warning(f"源文件不存在，跳过移动: {file_path}")
                        continue
                    if not os.path.exists(target_dir):
                        os.makedirs(target_dir, exist_ok=True)
                        self.logger.info(f"创建目标目录: {target_dir}")
                    if os.path.exists(target_path) and file_path != target_path:
                        failed_count += 1
                        failed_files.append(f"{file_path} (目标文件已存在)")
                        self.logger.warning(f"目标文件已存在，跳过移动: {target_path}")
                        continue
                    if file_path == target_path:
                        self.logger.info(f"文件已在目标位置，跳过: {file_path}")
                        continue
                    import shutil
                    shutil.move(file_path, target_path)
                    moved_count += 1
                    self.logger.info(f"已移动文件: {file_path} -> {target_path}")
                    self.event_system.publish("file_moved", {
                        "file_path": file_path,
                        "target_file_path": target_path,
                        "success": True
                    })
                except Exception as e:
                    failed_count += 1
                    failed_files.append(f"{file_path} ({str(e)})")
                    self.logger.error(f"移动文件失败: {file_path} -> {target_path}, 错误: {str(e)}")
                    self.event_system.publish("file_moved", {
                        "file_path": file_path,
                        "target_file_path": target_path,
                        "success": False,
                        "error": str(e)
                    })
            success_msg = f"移动完成: 成功移动 {moved_count} 个文件"
            if failed_count > 0:
                success_msg += f"，失败 {failed_count} 个文件"
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": success_msg,
                    "task": "移动完成",
                    "subtask": f"成功: {moved_count}, 失败: {failed_count}",
                    "log": success_msg,
                    "log_level": "success" if failed_count == 0 else "warning"
                }
            })
            result_message = success_msg
            if failed_files:
                result_message += "\n\n失败的文件:\n" + "\n".join(failed_files[:10])
                if len(failed_files) > 10:
                    result_message += f"\n... 还有 {len(failed_files) - 10} 个文件移动失败"
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "move_files",
                    "success": failed_count == 0,
                    "message": result_message,
                    "moved_count": moved_count,
                    "failed_count": failed_count,
                    "failed_files": failed_files
                }
            })
        except Exception as e:
            error_msg = f"移动文件时出错: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": error_msg,
                    "task": "移动出错",
                    "subtask": str(e),
                    "log": f"{error_msg}\n{traceback.format_exc()}",
                    "log_level": "error"
                }
            })
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "operation": "move_files",
                    "success": False,
                    "message": error_msg
                }
            })

    def init_mongodb(self):
        """初始化MongoDB数据库"""
        try:
            if not self.db_manager:
                self.log_message("MongoDB管理器未初始化", "error")
                return

            # 检查数据库是否为空
            if self.db_manager is not None and self.db_manager.collection.count_documents({}) > 0:
                self.log_message("数据库已包含数据，无需初始化", "warning")
                return

            # 创建索引
            # if hasattr(self.db_manager, 'create_indexes'):
            #     self.db_manager.create_indexes()
            # self.log_message("数据库索引创建完成", "info")

            # 从配置文件加载默认规则
            if os.path.exists(self.rules_file):
                self.log_message("正在从配置文件加载默认规则...", "info")
                # default_rules = self.config_loader.get_config("default_rules")
                default_rules = None
                if default_rules and self.db_manager is not None:
                    # 插入默认规则
                    self.db_manager.insert_file_info({
                        "path": "default",
                        "is_junk": False,
                        "renamed": False,
                        "is_whitelist": False,
                        "rules": default_rules,
                        # "created_time": self.db_manager.get_current_time(),
                        # "updated_time": self.db_manager.get_current_time()
                    })
                    self.log_message("默认规则加载完成", "success")
                else:
                    self.log_message("未找到默认规则配置", "warning")
            else:
                self.log_message(f"找不到默认规则配置文件: {self.rules_file}", "warning")

            self.log_message("MongoDB初始化完成", "success")
            
            # 初始化完成后刷新文件树
            self.root.after(1000, self.load_all_files_from_database)

        except Exception as e:
            self.log_message(f"初始化MongoDB失败: {str(e)}", "error")
            self.logger.error(f"初始化MongoDB时发生错误: {str(e)}")

    def refresh_database(self):
        """刷新数据库并刷新所有面板"""
        self.load_database_async()
        self.refresh_all()
        # 刷新数据库后刷新文件树
        self.root.after(1000, self.load_all_files_from_database)
        # 数据库刷新后刷新统计
        try:
            if self.db_manager is not None:
                stats = self.db_manager.get_global_stats()
                if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                    self.file_tree_panel.update_stats(stats)
        except Exception as e:
            self.logger.error(f"刷新文件统计信息失败: {e}")

    def clear_database(self):
        """清空数据库内容"""
        try:
            if not self.db_manager:
                self.log_message("MongoDB管理器未初始化", "error")
                return
            
            # 确认清空操作
            from tkinter import messagebox
            if not messagebox.askyesno("确认", "确定要清空数据库中的所有数据吗？此操作不可恢复！"):
                return
            
            # 异步执行清空操作
            self.log_message("开始清空数据库...", "info")
            
            # 通过任务队列异步执行
            self.task_queue.put({
                "type": "clear_database",
                "data": {}
            })
            
        except Exception as e:
            error_msg = f"清空数据库失败: {str(e)}"
            self.log_message(error_msg, "error")
            self.logger.error(f"清空数据库时发生错误: {str(e)}")
            messagebox.showerror("错误", error_msg)

    def load_database_async(self):
        """异步加载数据库信息"""
        try:
            if not self.db_manager:
                self.log_message("MongoDB管理器未初始化", "error")
                return
            
            # 检查数据库连接状态
            try:
                if self.db_manager is not None and self.db_manager.check_connection_health():
                    self.log_message("数据库连接正常", "success")
                    self.update_mongodb_connection_status(True)
                else:
                    self.log_message("数据库连接失败", "error")
                    self.update_mongodb_connection_status(False)
                    return
            except Exception as e:
                self.log_message(f"检查数据库连接失败: {str(e)}", "error")
                self.update_mongodb_connection_status(False)
                return
            
            # 获取数据库统计信息
            try:
                if self.db_manager is not None:
                    total_documents = self.db_manager.collection.count_documents({})
                    self.log_message(f"数据库中共有 {total_documents} 条记录", "info")
                
                # 更新操作状态
                if self.operation_status_label is not None and hasattr(self.operation_status_label, 'config'):
                    self.operation_status_label.config(text=f"操作状态: 已加载 {total_documents} 条记录")
                
                # 读取数据后刷新文件树
                self.root.after(1000, self.load_all_files_from_database)
                
            except Exception as e:
                self.log_message(f"获取数据库统计信息失败: {str(e)}", "error")
                
        except Exception as e:
            error_msg = f"加载数据库信息失败: {str(e)}"
            self.log_message(error_msg, "error")
            self.logger.error(f"加载数据库信息时发生错误: {str(e)}")

    def on_dir_list_mousewheel(self, event, shift: bool = False) -> str:
        """目录列表鼠标滚轮事件处理（占位）"""
        return "break"

    def on_log_mousewheel(self, event, shift: bool = False) -> str:
        """日志文本框鼠标滚轮事件处理（占位）"""
        return "break"

    def log_message(self, message, level="info"):
        """日志消息输出"""
        try:
            if not hasattr(self, 'log_text') or not self.log_text:
                return
            
            # 获取当前时间
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # 根据日志级别设置颜色
            color_map = {
                "info": "black",
                "warning": "orange",
                "error": "red",
                "success": "green",
                "debug": "gray"
            }
            color = color_map.get(level, "black")
            
            # 格式化日志消息
            formatted_message = f"[{timestamp}] {level.upper()}: {message}\n"
            
            # 在主线程中更新UI
            def update_log():
                try:
                    if self.log_text is not None and hasattr(self.log_text, 'config'):
                        self.log_text.config(state=tk.NORMAL)
                    if self.log_text is not None and hasattr(self.log_text, 'insert'):
                        self.log_text.insert(tk.END, formatted_message)
                    if self.log_text is not None and hasattr(self.log_text, 'see'):
                        self.log_text.see(tk.END)  # 滚动到最新内容
                    if self.log_text is not None and hasattr(self.log_text, 'config'):
                        self.log_text.config(state=tk.DISABLED)
                except Exception as e:
                    print(f"更新日志失败: {e}")
            
            # 确保在主线程中执行
            if hasattr(self, 'root') and self.root:
                self.root.after(0, update_log)
            else:
                update_log()
                
        except Exception as e:
            print(f"日志消息输出失败: {e}")

    def batch_log_messages(self, messages):
        """批量更新日志消息
        
        Args:
            messages (list): 包含(消息, 级别)元组的列表
        """
        if not messages or not hasattr(self, 'log_text') or not self.log_text:
            return
        
        try:
            # 获取当前时间
            import datetime
            
            # 一次性构建所有日志消息
            formatted_messages = ""
            for message, level in messages:
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                formatted_messages += f"[{timestamp}] {level.upper()}: {message}\n"
            
            # 在主线程中批量更新UI
            def update_log_batch():
                try:
                    if self.log_text is not None and hasattr(self.log_text, 'config'):
                        self.log_text.config(state=tk.NORMAL)
                    if self.log_text is not None and hasattr(self.log_text, 'insert'):
                        self.log_text.insert(tk.END, formatted_messages)
                    if self.log_text is not None and hasattr(self.log_text, 'see'):
                        self.log_text.see(tk.END)  # 滚动到最新内容
                    if self.log_text is not None and hasattr(self.log_text, 'config'):
                        self.log_text.config(state=tk.DISABLED)
                except Exception as e:
                    print(f"批量更新日志失败: {e}")
            
            # 确保在主线程中执行
            if hasattr(self, 'root') and self.root:
                self.root.after(0, update_log_batch)
            else:
                update_log_batch()
        except Exception as e:
            print(f"批量日志消息输出失败: {e}")

    def _initialize_db_status_monitor(self):
        """初始化数据库状态监控器"""
        try:
            if self.db_manager:
                from src.core.db_status_monitor import DatabaseStatusMonitor
                
                # 创建状态监控器
                self.db_status_monitor = DatabaseStatusMonitor(self.db_manager)
                
                # 添加状态回调
                self.db_status_monitor.add_status_callback(self._on_db_status_changed)
                self.db_status_monitor.add_hash_calculation_callback(self._on_hash_calculation_progress)
                
                # 延迟启动监控，确保主窗口完全初始化
                if hasattr(self, 'root') and self.root:
                    self.root.after(1000, self._start_db_status_monitoring)
                else:
                    # 如果没有root，直接启动（这种情况应该很少见）
                    self._start_db_status_monitoring()
                
                self.logger.info("数据库状态监控器初始化完成")
            else:
                self.logger.warning("数据库管理器未初始化，无法启动状态监控")
        except Exception as e:
            self.logger.error(f"初始化数据库状态监控器失败: {e}")
    
    def _start_db_status_monitoring(self):
        """启动数据库状态监控"""
        try:
            if hasattr(self, 'db_status_monitor') and self.db_status_monitor:
                # 启动监控
                self.db_status_monitor.start_monitoring()

                self.logger.info("数据库状态监控已启动")

                # 延迟启动自动文件树加载，确保所有组件完全初始化
                self.logger.info("数据库状态监控启动完成，准备延迟启动文件树加载...")
                self.root.after(100, self._schedule_delayed_file_tree_loading)

        except Exception as e:
            self.logger.error(f"启动数据库状态监控失败: {e}")

    def _schedule_delayed_file_tree_loading(self):
        """调度延迟的文件树加载"""
        try:
            # 检查所有关键组件是否已初始化
            components_ready = self._check_components_initialization()

            if components_ready:
                self.logger.info("所有组件初始化完成，开始检查自动加载设置...")

                # 检查是否启用自动加载文件树
                auto_load_enabled = self._should_auto_load_file_tree()

                if auto_load_enabled:
                    self.logger.info("自动加载文件树已启用，延迟2秒后开始加载...")
                    # 延迟2秒加载，减少等待时间
                    self.root.after(2000, self._auto_load_file_tree_with_interrupt_check)
                else:
                    self.logger.info("自动加载文件树已禁用，跳过启动时加载")
            else:
                self.logger.info("组件尚未完全初始化，延迟500ms后重新检查...")
                # 如果组件未完全初始化，延迟500ms后重新检查
                self.root.after(500, self._schedule_delayed_file_tree_loading)

        except Exception as e:
            self.logger.error(f"调度延迟文件树加载失败: {e}")

    def _check_components_initialization(self):
        """检查关键组件是否已初始化"""
        try:
            # 检查进度管理系统
            if not hasattr(self, 'progress_manager') or not self.progress_manager:
                self.logger.debug("进度管理器未初始化")
                return False

            # 检查任务概览面板
            if not hasattr(self, 'task_overview_panel') or not self.task_overview_panel:
                self.logger.debug("任务概览面板未初始化")
                return False

            # 检查状态栏
            if not hasattr(self, 'status_bar') or not self.status_bar:
                self.logger.debug("状态栏未初始化")
                return False

            # 检查数据库连接
            if not hasattr(self, 'db_manager') or not self.db_manager:
                self.logger.debug("数据库管理器未初始化")
                return False

            # 检查数据库状态监控
            if not hasattr(self, 'db_status_monitor') or not self.db_status_monitor:
                self.logger.debug("数据库状态监控器未初始化")
                return False

            # 检查中断功能
            if not hasattr(self, 'interrupt_event'):
                self.logger.debug("中断事件未初始化")
                return False

            # 检查UI是否完全渲染
            try:
                self.root.update_idletasks()
                if self.root.winfo_width() <= 1 or self.root.winfo_height() <= 1:
                    self.logger.debug("UI尚未完全渲染")
                    return False
            except Exception:
                self.logger.debug("UI状态检查失败")
                return False

            self.logger.info("所有关键组件初始化检查通过")
            return True

        except Exception as e:
            self.logger.error(f"检查组件初始化状态失败: {e}")
            return False

    def _should_auto_load_file_tree(self):
        """检查是否应该自动加载文件树"""
        try:
            # 从配置中读取设置，默认启用
            if hasattr(self, 'config_loader') and self.config_loader:
                config = self.config_loader.get_config('settings')  # 添加配置名称参数
                return config.get('ui', {}).get('auto_load_file_tree', True)
            return True  # 默认启用
        except Exception as e:
            self.logger.error(f"检查自动加载设置失败: {e}")
            return True  # 出错时默认启用

    def _auto_load_file_tree_with_interrupt_check(self):
        """带中断检查的自动加载文件树"""
        try:
            # 检查是否已被中断
            if hasattr(self, 'interrupt_event') and self.interrupt_event.is_set():
                self.logger.info("启动时文件树自动加载被中断，跳过加载")
                return

            # 检查是否已有加载任务在运行
            if hasattr(self, 'file_tree_loading') and self.file_tree_loading:
                self.logger.info("文件树加载任务已在运行，跳过自动加载")
                return

            self.logger.info("开始启动时自动加载文件树")
            # 延迟执行，避免阻塞初始化
            self.root.after(200, self.load_all_files_from_database)

        except Exception as e:
            self.logger.error(f"启动时自动加载文件树失败: {e}")
    
    def _on_db_status_changed(self, status):
        """数据库状态变化回调"""
        try:
            # 确保在主线程中执行UI更新
            if hasattr(self, 'root') and self.root:
                self.root.after(0, lambda: self._update_db_status_ui(status))
            else:
                self._update_db_status_ui(status)
        except Exception as e:
            self.logger.error(f"处理数据库状态变化失败: {e}")
    
    def _update_db_status_ui(self, status):
        """在主线程中更新数据库状态UI"""
        try:
            # 缓存上一次的状态，避免重复更新
            if not hasattr(self, '_last_db_status'):
                self._last_db_status = None
            
            # 检查状态是否真的发生了变化
            current_status_key = (
                status.is_connected,
                status.total_files,
                status.files_with_hash,
                round(status.hash_completion_percentage, 1)
            )
            
            if self._last_db_status == current_status_key:
                # 状态没有变化，跳过更新
                return
            
            # 更新缓存的状态
            self._last_db_status = current_status_key
            
            if self.status_panel:
                # 更新数据库连接状态灯
                connection_status = "green" if status.is_connected else "red"
                connection_tooltip = "数据库连接正常" if status.is_connected else f"数据库连接失败: {status.error_message}"
                self.status_panel.update_status("db_connection", connection_status, connection_tooltip)
                
                # 更新hash完整性状态灯
                hash_status = "green" if status.hash_completion_percentage >= 100.0 else "red"
                hash_tooltip = f"Hash完整性: {status.hash_completion_percentage:.1f}% ({status.files_with_hash}/{status.total_files})"
                self.status_panel.update_status("hash_completeness", hash_status, hash_tooltip)
                
                # 更新hash完整性进度条
                progress = status.hash_completion_percentage / 100.0
                color = "green" if progress >= 1.0 else "orange" if progress >= 0.5 else "red"
                self.status_panel.update_progress("hash_progress", progress, color)
            
            # 更新重复文件查找按钮状态
            self._update_duplicate_button_state(status)
            
        except Exception as e:
            self.logger.error(f"更新数据库状态UI失败: {e}")
    
    def _on_hash_calculation_progress(self, progress, message):
        """Hash计算进度回调"""
        try:
            # 确保在主线程中执行UI更新
            if hasattr(self, 'root') and self.root:
                self.root.after(0, lambda: self._update_hash_progress_ui(progress, message))
            else:
                self._update_hash_progress_ui(progress, message)
        except Exception as e:
            self.logger.error(f"处理Hash计算进度失败: {e}")
    
    def _update_hash_progress_ui(self, progress, message):
        """在主线程中更新Hash计算进度UI"""
        try:
            # 确保进度值在有效范围内
            progress = max(0.0, min(1.0, progress))
            
            if self.status_panel:
                # 更新进度条
                color = "green" if progress >= 1.0 else "orange" if progress >= 0.5 else "red"
                self.status_panel.update_progress("hash_progress", progress, color)
            
            # 更新状态栏
            if hasattr(self, 'task_label') and self.task_label is not None:
                self.task_label.set("Hash值计算")
            if hasattr(self, 'subtask_label') and self.subtask_label is not None:
                self.subtask_label.set(message)
            if hasattr(self, 'progress_var') and self.progress_var is not None:
                self.progress_var.set(progress * 100)
            
            # 启用中止按钮
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.enable_stop_button()
            
            # 记录进度日志
            self.logger.debug(f"Hash值计算进度: {progress*100:.1f}% - {message}")
            
            # hash计算完成时补全进度条
            if progress >= 100 and hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.update_progress(100, "Hash计算完成")
            
        except Exception as e:
            self.logger.error(f"更新Hash计算进度UI失败: {e}")
    
    def _update_duplicate_button_state(self, status):
        """更新重复文件查找按钮状态"""
        try:
            if hasattr(self, 'duplicate_panel') and self.duplicate_panel:
                if status.is_connected:
                    # 检查是否有实际存在的文件缺少hash值
                    # 如果hash完整性超过95%，认为可以正常使用（忽略不存在的测试文件）
                    if status.hash_completion_percentage >= 95.0:
                        # 数据库连接正常且hash完整性足够高
                        self.duplicate_panel.set_find_button_state(True, "查找重复文件")
                    else:
                        # 缺少hash值的文件较多
                        self.duplicate_panel.set_find_button_state(False, f"缺少Hash值({status.files_without_hash})")
                else:
                    # 数据库连接失败
                    self.duplicate_panel.set_find_button_state(False, "数据库未连接")
        except Exception as e:
            self.logger.error(f"更新重复文件按钮状态失败: {e}")
    
    def cleanup_database_nonexistent_files(self):
        """清理数据库中不存在的文件记录"""
        try:
            if not self.db_manager:
                messagebox.showerror("错误", "数据库管理器未初始化")
                return
            
            # 确认操作
            response = messagebox.askyesno(
                "确认清理", 
                "此操作将检查数据库中所有文件记录，删除不存在的文件记录。\n\n"
                "这可能需要一些时间，是否继续？"
            )
            
            if not response:
                return
            
            # 在新线程中执行清理操作
            def run_cleanup():
                try:
                    self.logger.info("开始清理数据库中不存在的文件记录...")
                    
                    # 更新状态
                    self.result_queue.put({
                        "type": "progress",
                        "data": {
                            "progress": 0,
                            "status": "正在清理数据库...",
                            "task": "数据库清理",
                            "subtask": "检查文件存在性",
                            "log": "开始清理数据库中不存在的文件记录"
                        }
                    })
                    
                    # 执行清理
                    if self.db_manager:
                        result = self.db_manager.cleanup_nonexistent_files(batch_size=100)
                    else:
                        raise Exception("数据库管理器未初始化")
                    
                    # 更新状态
                    self.result_queue.put({
                        "type": "progress",
                        "data": {
                            "progress": 100,
                            "status": "数据库清理完成",
                            "task": "数据库清理",
                            "subtask": "完成",
                            "log": f"数据库清理完成: 检查 {result['total_checked']} 个文件，删除 {result['total_deleted']} 个不存在文件的记录"
                        }
                    })
                    
                    # 发送完成消息
                    self.result_queue.put({
                        "type": "operation_complete",
                        "data": {
                            "success": True,
                            "message": f"数据库清理完成！\n\n"
                                      f"检查文件数: {result['total_checked']}\n"
                                      f"删除记录数: {result['total_deleted']}\n"
                                      f"存在文件数: {result['total_exist']}"
                        }
                    })
                    
                    # 刷新文件树
                    self.result_queue.put({
                        "type": "refresh_file_tree",
                        "data": {
                            "message": "数据库已清理，刷新文件树"
                        }
                    })
                    
                except Exception as e:
                    self.logger.error(f"数据库清理失败: {e}")
                    self.result_queue.put({
                        "type": "operation_complete",
                        "data": {
                            "success": False,
                            "message": f"数据库清理失败: {str(e)}"
                        }
                    })
            
            # 启动清理线程
            import threading
            cleanup_thread = threading.Thread(target=run_cleanup, daemon=True)
            cleanup_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动数据库清理失败: {e}")
            messagebox.showerror("错误", f"启动数据库清理失败: {str(e)}")

    def on_db_operation_complete(self, data):
        """处理数据库操作完成事件"""
        try:
            operation_type = data.get("operation_type", "")
            folder_path = data.get("folder_path", "")
            result = data.get("result", {})
            success = data.get("success", True)
            
            if success:
                self.logger.info(f"数据库操作完成: {operation_type}, 文件夹: {folder_path}")
                
                # 检查是否正在扫描中，如果是则不自动刷新文件树
                # 扫描过程中的数据库操作由扫描流程统一处理
                if hasattr(self, 'scanning_in_progress') and self.scanning_in_progress:
                    self.logger.info("扫描进行中，跳过自动刷新文件树（由扫描流程统一处理）")
                    return
                
                # 根据操作类型决定是否需要刷新文件树
                if operation_type in ["insert", "update", "delete", "batch_upsert"]:
                    self.logger.info(f"数据库操作完成: {operation_type}，刷新文件树")
                    # 延迟刷新，避免频繁刷新
                    self.root.after(500, self.load_all_files_from_database)
                elif operation_type == "clear":
                    # 清空数据库是特殊操作，需要立即刷新
                    self.logger.info("数据库清空操作完成，立即刷新文件树")
                    # 使用多个延迟调用，确保刷新成功
                    self.root.after(100, self.load_all_files_from_database)
                    self.root.after(500, self.load_all_files_from_database)
                    self.root.after(1000, self.load_all_files_from_database)
                    
                    # 触发文件树刷新事件
                    self.result_queue.put({
                        "type": "refresh_file_tree",
                        "data": {
                            "message": "数据库已清空，刷新文件树"
                        }
                    })
            else:
                self.logger.error(f"数据库操作失败: {operation_type}, 文件夹: {folder_path}")
                messagebox.showerror("数据库操作失败", f"操作类型: {operation_type}\n文件夹: {folder_path}")
            
        except Exception as e:
            self.logger.error(f"处理数据库操作完成事件失败: {e}")

    def on_db_stats_updated(self, data):
        """处理数据库统计信息更新事件"""
        try:
            # 更新统计信息显示
            stats = data.get("stats", {})
            if stats:
                self.update_file_stats(stats)
                
        except Exception as e:
            self.logger.error(f"处理数据库统计信息更新事件失败: {e}")

    def on_db_operation_error(self, data):
        """处理数据库操作错误事件"""
        try:
            error_msg = data.get("error", "未知错误")
            self.logger.error(f"数据库操作错误: {error_msg}")
            
            # 显示错误消息
            messagebox.showerror("数据库错误", f"数据库操作失败: {error_msg}")
            
        except Exception as e:
            self.logger.error(f"处理数据库操作错误事件失败: {e}")

    def clear_file_tree_display(self):
        """强制清空文件树显示"""
        try:
            self.logger.info("强制清空文件树显示")
            if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                # 清空文件树显示
                empty_data = {
                    'files': {},
                    'directory': '',
                    'file_count': 0,
                    'video_count': 0,
                    'junk_count': 0,
                    'whitelist_count': 0
                }
                self.file_tree_panel.update_file_tree(empty_data)
                self.logger.info("文件树显示已强制清空")

            # 更新状态栏
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message("文件树已清空")

        except Exception as e:
            self.logger.error(f"强制清空文件树显示失败: {e}")

    def refresh_file_tree(self):
        """手动刷新文件树"""
        try:
            self.logger.info("手动刷新文件树")
            self.load_all_files_from_database()

            # 更新状态栏
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.show_message("文件树已刷新")
                
            # 右键菜单刷新后刷新统计
            try:
                if self.db_manager is not None:
                    stats = self.db_manager.get_global_stats()
                    if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                        self.file_tree_panel.update_stats(stats)
            except Exception as e:
                self.logger.error(f"刷新文件统计信息失败: {e}")
            # 刷新文件树后补全进度条
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.update_progress(100, "文件树刷新完成")
            
        except Exception as e:
            error_msg = f"刷新文件树失败: {e}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def _on_db_progress(self, progress_data: Dict[str, Any]):
        """数据库操作进度回调"""
        try:
            operation = progress_data.get("operation", "unknown")
            current = progress_data.get("current", 0)
            total = progress_data.get("total", 0)
            message = progress_data.get("message", "")
            percentage = progress_data.get("percentage", 0)
            
            self.logger.debug(f"数据库操作进度: {operation} - {current}/{total} ({percentage:.1f}%) - {message}")
            
            # 更新操作状态
            if self.operation_status_label is not None and hasattr(self.operation_status_label, 'config'):
                self.operation_status_label.config(text=f"操作状态: {message}")
                
        except Exception as e:
            self.logger.error(f"处理数据库进度回调失败: {e}")

    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"

    def on_file_scanned(self, data):
        """处理文件扫描事件（占位）"""
        pass

    def on_scan_completed(self, data):
        """处理扫描完成事件"""
        try:
            # 获取扫描结果和统计信息
            scan_result = data.get("scan_result", {})
            stats = data.get("stats", {})

            # 更新文件树面板的统计信息
            if self.file_tree_panel and hasattr(self.file_tree_panel, 'update_stats'):
                self.file_tree_panel.update_stats(stats)

            # 更新文件树显示
            if scan_result:
                self.update_ui({
                    "type": "file_tree_update",
                    "data": scan_result
                })

            self.logger.info(f"扫描完成，统计信息: {stats}")
            self.logger.info("扫描完成 - 仅进行了文件信息收集和数据库存储")
            self.logger.info("垃圾文件检查、重复文件检查、白名单检查需要在对应面板中手动启动")

            # 注意：扫描完成后不会自动启动垃圾文件检查、重复文件检查、白名单检查
            # 这些功能需要用户在对应的面板中手动启动

        except Exception as e:
            self.logger.error(f"处理扫描完成事件失败: {e}")

    def on_db_connected(self, data):
        """处理数据库连接事件"""
        # 在主线程中更新UI
        self.root.after(0, lambda: self.update_mongodb_connection_status(True))
        self.logger.info("数据库连接成功")

    def on_db_disconnected(self, data):
        """处理数据库断开连接事件"""
        # 在主线程中更新UI
        self.root.after(0, lambda: self.update_mongodb_connection_status(False))
        self.logger.warning("数据库连接断开")

    def on_file_renamed(self, data):
        """处理文件重命名事件（占位）"""
        pass

    def on_file_deleted(self, data):
        """处理文件删除事件（占位）"""
        pass

    def on_file_moved(self, data):
        """处理文件移动事件（占位）"""
        pass

    def on_db_operation_start(self, data):
        """处理数据库操作开始事件（占位）"""
        pass

    def add_directory(self):
        """添加目录"""
        try:
            from tkinter import filedialog
            directory = filedialog.askdirectory(title="选择要添加的目录")
            if directory:
                # 检查目录是否已存在
                existing_dirs = []
                if self.dir_listbox and hasattr(self.dir_listbox, 'get'):
                    existing_dirs = list(self.dir_listbox.get(0, tk.END))
                if directory not in existing_dirs and self.dir_listbox and hasattr(self.dir_listbox, 'insert'):
                    self.dir_listbox.insert(tk.END, directory)
                    self.logger.info(f"添加目录: {directory}")
                else:
                    from tkinter import messagebox
                    messagebox.showwarning("警告", "该目录已存在")
        except Exception as e:
            self.logger.error(f"添加目录失败: {e}")
            from tkinter import messagebox
            messagebox.showerror("错误", f"添加目录失败: {e}")

    def remove_directory(self):
        """移除目录"""
        try:
            selected_indices = self.dir_listbox.curselection() if self.dir_listbox is not None and hasattr(self.dir_listbox, 'curselection') else []
            if not selected_indices:
                from tkinter import messagebox
                messagebox.showwarning("警告", "请先选择要移除的目录")
                return
            
            # 从后向前删除，避免索引变化
            for index in sorted(selected_indices, reverse=True):
                directory = None
                if self.dir_listbox is not None and hasattr(self.dir_listbox, 'get'):
                    directory = self.dir_listbox.get(index)
                if self.dir_listbox is not None and hasattr(self.dir_listbox, 'delete'):
                    self.dir_listbox.delete(index)
                self.logger.info(f"移除目录: {directory}")
        except Exception as e:
            self.logger.error(f"移除目录失败: {e}")
            from tkinter import messagebox
            messagebox.showerror("错误", f"移除目录失败: {e}")

    def clear_directories(self):
        """清空目录"""
        try:
            from tkinter import messagebox
            if messagebox.askyesno("确认", "确定要清空所有目录吗？"):
                if self.dir_listbox is not None and hasattr(self.dir_listbox, 'delete'):
                    self.dir_listbox.delete(0, tk.END)
                self.logger.info("清空所有目录")
        except Exception as e:
            self.logger.error(f"清空目录失败: {e}")
            from tkinter import messagebox
            messagebox.showerror("错误", f"清空目录失败: {e}")

    def scan_selected_directories(self):
        """扫描选中目录"""
        try:
            selected_indices = self.dir_listbox.curselection() if self.dir_listbox is not None and hasattr(self.dir_listbox, 'curselection') else []
            if not selected_indices:
                from tkinter import messagebox
                messagebox.showwarning("警告", "请先选择要扫描的目录")
                return
            
            selected_dirs = [self.dir_listbox.get(index) for index in selected_indices] if self.dir_listbox is not None and hasattr(self.dir_listbox, 'get') else []
            
            # 验证所有选中的目录是否有效
            import os
            invalid_dirs = [d for d in selected_dirs if not os.path.exists(d)]
            if invalid_dirs:
                from tkinter import messagebox
                messagebox.showerror("错误", f"以下目录无效或不存在：\n{chr(10).join(invalid_dirs)}")
                return
            
            # 开始扫描 - 通过任务队列执行
            self.logger.info(f"开始扫描选中的目录: {selected_dirs}")
            self.task_queue.put({
                "type": "scan_directory",
                "data": selected_dirs
            })
        except Exception as e:
            self.logger.error(f"扫描目录失败: {e}")
            from tkinter import messagebox
            messagebox.showerror("错误", f"扫描目录失败: {e}")

    def refresh_directories(self):
        """刷新目录"""
        try:
            all_dirs = list(self.dir_listbox.get(0, tk.END)) if self.dir_listbox is not None and hasattr(self.dir_listbox, 'get') else []
            if not all_dirs:
                from tkinter import messagebox
                messagebox.showwarning("警告", "目录列表为空，请先添加目录")
                return
            
            # 验证所有目录是否有效
            import os
            invalid_dirs = [d for d in all_dirs if not os.path.exists(d)]
            if invalid_dirs:
                from tkinter import messagebox
                messagebox.showerror("错误", f"以下目录无效或不存在：\n{chr(10).join(invalid_dirs)}")
                return
            
            # 开始扫描 - 通过任务队列执行
            self.logger.info(f"开始刷新所有目录: {all_dirs}")
            self.task_queue.put({
                "type": "scan_directory",
                "data": all_dirs
            })
        except Exception as e:
            self.logger.error(f"刷新目录失败: {e}")
            from tkinter import messagebox
            messagebox.showerror("错误", f"刷新目录失败: {e}")

    def update_mongodb_connection_status(self, is_connected: bool):
        """更新MongoDB连接状态Label"""
        try:
            # 检查状态是否真的发生了变化
            current_status = getattr(self, '_current_db_status', None)
            if current_status == is_connected:
                # 状态没有变化，不需要更新
                return
            
            # 更新状态
            self._current_db_status = is_connected
            
            if hasattr(self, 'mongodb_status_var'):
                if is_connected:
                    self.mongodb_status_var.set("连接状态: 已连接")
                    if hasattr(self, 'mongodb_status_label'):
                        self.mongodb_status_label.configure(foreground="green")
                else:
                    self.mongodb_status_var.set("连接状态: 未连接")
                    if hasattr(self, 'mongodb_status_label'):
                        self.mongodb_status_label.configure(foreground="red")
            
            # 记录状态变化
            self.logger.info(f"数据库连接状态更新: {'已连接' if is_connected else '未连接'}")
            
        except Exception as e:
            self.logger.error(f"更新数据库连接状态失败: {e}")

    def do_find_junk_files(self):
        """执行垃圾文件查找"""
        try:
            # 检查 file_scanner 是否初始化
            if self.file_scanner is None:
                self.logger.error("file_scanner 未初始化，无法查找垃圾文件")
                return
            
            # 记录开始时间
            start_time = time.time()
            
            # 添加日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": "开始查找垃圾文件",
                    "task": "查找垃圾文件",
                    "subtask": "准备中...",
                    "log": "开始查找垃圾文件",
                    "log_level": "info"
                }
            })
            
            # 查找垃圾文件
            directories = list(self.dir_listbox.get(0, tk.END)) if self.dir_listbox is not None else []
            junk_files = self.file_scanner.find_junk_files(directories)
            
            # 计算查找耗时
            elapsed_time = time.time() - start_time
            
            # 计算垃圾文件统计信息
            total_files = len(junk_files)
            total_size = sum(getattr(f, 'size', 0) for f in junk_files)
            
            # 按扩展名统计垃圾文件
            ext_stats = {}
            for file_info in junk_files:
                ext = getattr(file_info, 'extension', '').lower()
                if ext not in ext_stats:
                    ext_stats[ext] = 0
                ext_stats[ext] += 1
            
            # 构建扩展名统计信息
            ext_info = ""
            if ext_stats:
                ext_info = "\n文件类型统计:\n"
                for ext, count in sorted(ext_stats.items(), key=lambda x: x[1], reverse=True):
                    ext_info += f"  {ext}: {count} 个文件\n"
            
            # 添加完成日志
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 100,
                    "status": "垃圾文件查找完成",
                    "task": "查找完成",
                    "subtask": f"找到 {total_files} 个垃圾文件",
                    "log": f"垃圾文件查找完成，找到 {total_files} 个垃圾文件，总大小: {format_size(total_size)}，耗时: {elapsed_time:.2f} 秒{ext_info}",
                    "log_level": "success"
                }
            })
            
            # 将结果添加到结果队列
            self.result_queue.put({
                "type": "junk_files",
                "data": junk_files
            })
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"查找垃圾文件时出错: {error_msg}\n{traceback.format_exc()}")
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": f"查找垃圾文件出错: {error_msg}",
                    "task": "查找出错",
                    "subtask": error_msg,
                    "log": f"查找垃圾文件出错: {error_msg}",
                    "log_level": "error"
                }
            })
            
            # 将空结果放入队列
            self.result_queue.put({
                "type": "junk_files",
                "data": []
            })

    def test_database_connection(self):
        """测试数据库连接"""
        try:
            self.logger.info("开始测试数据库连接...")
            
            if not hasattr(self, 'db_manager') or self.db_manager is None:
                self.logger.warning("数据库管理器未初始化，尝试重新初始化...")
                # 重新初始化核心服务
                self._setup_core_services()
                
                if not hasattr(self, 'db_manager') or self.db_manager is None:
                    error_msg = "无法初始化数据库管理器"
                    self.logger.error(error_msg)
                    self.log_message(error_msg, "error")
                    self.update_mongodb_connection_status(False)
                    return
            
            # 检查数据库连接状态
            try:
                if self.db_manager.check_connection_health():
                    success_msg = "数据库连接测试成功"
                    self.logger.info(success_msg)
                    self.log_message(success_msg, "success")
                    self.update_mongodb_connection_status(True)
                    
                    # 更新操作状态
                    if self.operation_status_label is not None and hasattr(self.operation_status_label, 'config'):
                        self.operation_status_label.config(text="操作状态: 连接正常")
                else:
                    error_msg = "数据库连接测试失败"
                    self.logger.error(error_msg)
                    self.log_message(error_msg, "error")
                    self.update_mongodb_connection_status(False)
                    
                    # 更新操作状态
                    if self.operation_status_label is not None and hasattr(self.operation_status_label, 'config'):
                        self.operation_status_label.config(text="操作状态: 连接失败")
                        
            except Exception as e:
                error_msg = f"数据库连接测试异常: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(f"异常详情: {traceback.format_exc()}")
                self.log_message(error_msg, "error")
                self.update_mongodb_connection_status(False)
                
                # 更新操作状态
                if self.operation_status_label is not None and hasattr(self.operation_status_label, 'config'):
                    self.operation_status_label.config(text="操作状态: 连接异常")
                    
        except Exception as e:
            error_msg = f"测试数据库连接失败: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            self.log_message(error_msg, "error")
            self.update_mongodb_connection_status(False)

    def do_clear_database(self):
        """异步执行数据库清空操作"""
        try:
            self.logger.info("开始清空数据库...")
            
            # 检查数据库管理器
            if not self.db_manager:
                error_msg = "MongoDB管理器未初始化"
                self.logger.error(error_msg)
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": 0,
                        "status": error_msg,
                        "task": "清空数据库",
                        "subtask": "错误",
                        "log": error_msg,
                        "log_level": "error"
                    }
                })
                return
            
            # 检查数据库连接
            if not self.db_manager.check_connection_health():
                error_msg = "数据库连接失败"
                self.logger.error(error_msg)
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": 0,
                        "status": error_msg,
                        "task": "清空数据库",
                        "subtask": "连接错误",
                        "log": error_msg,
                        "log_level": "error"
                    }
                })
                return
            
            # 执行清空操作
            try:
                self.logger.info("执行数据库清空操作...")
                
                # 使用MongoDBManager的clear_database方法
                result = self.db_manager.clear_database(show_progress=True)
                deleted_count = result.get("deleted_count", 0)
                message = result.get("message", "数据库清空完成")
                
                success_msg = f"数据库清空完成，删除了 {deleted_count} 条记录"
                self.logger.info(success_msg)
                
                # 重要：清空内存中的文件路径缓存
                if hasattr(self, '_current_db_file_paths'):
                    self._current_db_file_paths = set()
                    self.logger.info("已清空内存中的文件路径缓存")
                
                # 发送完成消息
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": 100,
                        "status": "数据库清空完成",
                        "task": "清空完成",
                        "subtask": f"删除了 {deleted_count} 条记录",
                        "log": success_msg,
                        "log_level": "success"
                    }
                })
                # 发送操作完成消息
                self.result_queue.put({
                    "type": "operation_complete",
                    "data": {
                        "message": success_msg,
                        "success": True
                    }
                })
                
                # 立即清空文件树缓存
                self.result_queue.put({
                    "type": "clear_file_tree_cache",
                    "data": {}
                })

                # 清空数据库完成后强制刷新文件树
                self.result_queue.put({
                    "type": "refresh_file_tree",
                    "data": {
                        "message": "数据库已清空，强制刷新文件树",
                        "force_refresh": True
                    }
                })

                # 确保清空数据库后立即刷新文件树
                # 先立即清空显示，再重新加载
                self.root.after(50, self.clear_file_tree_display)
                self.root.after(200, self.load_all_files_from_database)
                self.root.after(500, self.load_all_files_from_database)
                
            except Exception as e:
                error_msg = f"清空数据库操作失败: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                
                self.result_queue.put({
                    "type": "progress",
                    "data": {
                        "progress": 0,
                        "status": error_msg,
                        "task": "清空数据库",
                        "subtask": "错误",
                        "log": error_msg,
                        "log_level": "error"
                    }
                })
                self.result_queue.put({
                    "type": "operation_complete",
                    "data": {
                        "message": error_msg,
                        "success": False
                    }
                })
                
        except Exception as e:
            error_msg = f"清空数据库时发生未知错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            
            self.result_queue.put({
                "type": "progress",
                "data": {
                    "progress": 0,
                    "status": error_msg,
                    "task": "清空失败",
                    "subtask": "未知错误",
                    "log": error_msg,
                    "log_level": "error"
                }
            })
            
            # 发送操作失败消息
            self.result_queue.put({
                "type": "operation_complete",
                "data": {
                    "message": error_msg,
                    "success": False
                }
            })

    def load_all_files_from_database(self):
        """从数据库加载所有文件并更新文件树（异步版本）"""
        if self.file_tree_loading:
            self.logger.info("文件树加载任务已在运行，跳过重复请求")
            return

        # 增强的重复调用检查
        current_time = time.time()
        if hasattr(self, '_last_load_time'):
            time_diff = current_time - self._last_load_time
            if time_diff < 2.0:  # 2秒内的重复调用
                self.logger.info(f"检测到短时间内的重复加载请求(间隔{time_diff:.1f}s)，跳过")
                return

        self._last_load_time = current_time

        self.logger.info("[文件树加载] 开始异步加载 - 准备从数据库获取文件信息")
        self.file_tree_loading = True
        
        # 1. 为任务创建ID并注册
        task_id = f"file_tree_load_{int(time.time() * 1000)}"
        self.progress_manager.register_task(
            task_id=task_id,
            task_type=TaskType.FILE_TREE_LOAD,
            task_name="文件树加载",
            total=100  # 使用百分比作为总数
        )
        self.progress_manager.start_task(task_id)

        if hasattr(self, 'status_bar') and self.status_bar:
            self.status_bar.update_progress(0, "正在加载文件树...")
            self.status_bar.enable_stop_button()

        async def load_task():
            try:
                # 2. 将task_id传递给核心加载方法
                await self._load_files_from_database_async(self.interrupt_event, task_id)
                self.progress_manager.complete_task(task_id, success=True, message="文件树加载完成")
            except asyncio.CancelledError:
                self.logger.warning(f"文件树加载任务 {task_id} 被取消")
                self.progress_manager.cancel_task(task_id, "任务被用户取消")
            except Exception as e:
                self.logger.error(f"文件树加载任务失败: {e}", exc_info=True)
                # 使用正确的API来标记任务失败
                self.progress_manager.complete_task(task_id, success=False, message=f"加载失败: {e}")
            finally:
                self.file_tree_loading = False
                if hasattr(self, 'status_bar') and self.status_bar:
                    self.status_bar.disable_stop_button()

        # 提交异步任务（修复协程警告）
        self._submit_file_tree_load_task_safely(load_task, task_id)

    async def _submit_file_tree_load_task(self, load_task):
        """安全提交文件树加载任务"""
        try:
            from src.utils.unified_task_manager import UnifiedTaskManager
            manager = UnifiedTaskManager()
            # 修复：传递协程对象，而不是调用结果
            task_id = await manager.submit(load_task, use_coroutine=True)
            return task_id
        except Exception as e:
            self.logger.error(f"提交文件树加载任务失败: {e}")
            return None

    def _submit_file_tree_load_task_safely(self, load_task, internal_task_id):
        """安全提交文件树加载任务（同步版本）"""
        try:
            # 检查异步管理器是否可用
            if hasattr(self, 'async_manager') and self.async_manager and self.async_manager.is_available():
                try:
                    loop = self.async_manager.get_event_loop()
                    if loop and loop.is_running():
                        self.logger.info("尝试提交异步任务到事件循环...")
                        # 直接在事件循环中执行协程，不等待结果
                        future = asyncio.run_coroutine_threadsafe(load_task(), loop)
                        self.current_task_id = internal_task_id
                        self.logger.info(f"已直接提交文件树加载任务到事件循环，任务ID: {internal_task_id}")

                        # 添加完成回调，确保任务状态正确更新
                        def on_task_complete(fut):
                            try:
                                result = fut.result()
                                self.logger.info(f"异步文件树加载任务完成: {internal_task_id}")
                            except Exception as e:
                                self.logger.error(f"异步文件树加载任务失败: {e}")
                                # 如果异步失败，启动同步备用方案
                                self.root.after(0, lambda: self._execute_load_task_sync(internal_task_id))

                        future.add_done_callback(on_task_complete)
                        return
                    else:
                        self.logger.warning("事件循环未运行，使用同步备用方案")
                except Exception as loop_error:
                    self.logger.error(f"提交到事件循环失败: {loop_error}，使用同步备用方案")
            else:
                self.logger.warning("异步管理器不可用，使用同步备用方案")

            # 降级处理：使用同步备用方案
            self.current_task_id = internal_task_id
            self.logger.info(f"使用内部任务ID: {internal_task_id}")

            # 立即执行同步备用方案
            self.logger.info("启动同步备用方案...")
            self.root.after(100, lambda: self._execute_load_task_sync(internal_task_id))

        except Exception as e:
            self.logger.error(f"安全提交文件树加载任务失败: {e}，使用同步备用方案")
            self.current_task_id = internal_task_id
            self.root.after(100, lambda: self._execute_load_task_sync(internal_task_id))

    def _execute_load_task_sync(self, task_id):
        """同步执行文件树加载任务（备用方案）"""
        try:
            self.logger.info(f"开始同步执行文件树加载任务: {task_id}")

            # 更新进度
            if hasattr(self, 'progress_manager') and self.progress_manager:
                self.progress_manager.update_progress(task_id, progress=5, status_message="从数据库获取数据...")

            # 同步获取文件数据
            if not self.db_manager:
                self.logger.warning("数据库管理器未初始化")
                if hasattr(self, 'progress_manager') and self.progress_manager:
                    self.progress_manager.complete_task(task_id, success=False, message="数据库管理器未初始化")
                return

            self.logger.info("开始从数据库获取文件数据...")
            files_data = self.db_manager.get_all_files()
            self.logger.info(f"同步获取到 {len(files_data) if files_data else 0} 条文件记录")

            if not files_data:
                self.logger.warning("数据库查询返回空结果")
                if hasattr(self, 'progress_manager') and self.progress_manager:
                    self.progress_manager.complete_task(task_id, success=False, message="数据库为空")
                return

            # 更新进度
            if hasattr(self, 'progress_manager') and self.progress_manager:
                self.progress_manager.update_progress(task_id, progress=50, status_message="处理文件数据...")

            # 处理文件数据
            self.logger.info("开始处理文件数据...")
            files_dict = {}
            video_count = 0
            junk_count = 0
            whitelist_count = 0
            total_size = 0
            video_total_size = 0

            for i, f in enumerate(files_data):
                path = f.get("path", "")
                if not path:
                    continue

                files_dict[path] = f
                size = f.get("size", 0)
                total_size += size

                if f.get("is_video"):
                    video_count += 1
                    video_total_size += size
                if f.get("is_junk"):
                    junk_count += 1
                if f.get("is_whitelist"):
                    whitelist_count += 1

                # 定期更新进度
                if i % 100 == 0:
                    progress = 50 + (i / len(files_data)) * 40
                    if hasattr(self, 'progress_manager') and self.progress_manager:
                        self.progress_manager.update_progress(task_id, progress=progress,
                                                            status_message=f"处理文件数据... {i}/{len(files_data)}")

            processed_data = {
                'files': files_dict,
                'directory': '',
                'file_count': len(files_data),
                'total_size': total_size,
                'video_count': video_count,
                'video_total_size': video_total_size,
                'junk_count': junk_count,
                'whitelist_count': whitelist_count
            }

            self.logger.info(f"文件数据处理完成: {len(files_data)}个文件, {video_count}个视频, {junk_count}个垃圾文件, {whitelist_count}个白名单文件")

            # 更新进度
            if hasattr(self, 'progress_manager') and self.progress_manager:
                self.progress_manager.update_progress(task_id, progress=95, status_message="更新界面...")

            # 更新UI
            def update_ui():
                try:
                    if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                        self.file_tree_panel.update_file_tree(processed_data)
                        self.logger.info("文件树界面更新完成")

                    if hasattr(self, 'status_bar') and self.status_bar:
                        self.status_bar.update_progress(100, "文件树加载完成")
                        self.status_bar.disable_stop_button()

                    # 更新文件统计信息
                    self._update_file_stats(processed_data)

                    if hasattr(self, 'progress_manager') and self.progress_manager:
                        self.progress_manager.complete_task(task_id, success=True, message="加载成功")

                    self.file_tree_loading = False
                    self.logger.info(f"同步文件树加载任务完成: {task_id}")

                except Exception as ui_error:
                    self.logger.error(f"更新UI失败: {ui_error}")
                    if hasattr(self, 'progress_manager') and self.progress_manager:
                        self.progress_manager.complete_task(task_id, success=False, message=f"UI更新失败: {ui_error}")

            self.root.after(0, update_ui)

        except Exception as e:
            self.logger.error(f"同步执行文件树加载任务失败: {e}", exc_info=True)
            if hasattr(self, 'progress_manager') and self.progress_manager:
                self.progress_manager.complete_task(task_id, success=False, message=f"加载失败: {e}")
            self.file_tree_loading = False

    def _update_file_stats(self, processed_data):
        """更新文件统计信息"""
        try:
            if not processed_data:
                return

            stats = {
                'file_count': processed_data.get('file_count', 0),
                'total_size': processed_data.get('total_size', 0),
                'video_count': processed_data.get('video_count', 0),
                'video_total_size': processed_data.get('video_total_size', 0),
                'whitelist_count': processed_data.get('whitelist_count', 0),
                'junk_count': processed_data.get('junk_count', 0)
            }

            self.logger.info(f"已更新文件统计信息：{stats}")

            # 如果有状态栏，更新状态栏的统计信息
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.update_file_stats(stats)
                self.logger.info("状态栏统计信息更新完成")

            # 确保程序继续响应
            self.logger.info("文件统计信息更新完成，程序继续运行...")

            # 强制刷新UI
            if hasattr(self, 'root') and self.root:
                self.root.update_idletasks()
                self.logger.info("UI刷新完成")

        except Exception as e:
            self.logger.error(f"更新文件统计信息失败: {e}")

    def _show_load_error_on_ui(self, error):
        """在UI上显示加载错误"""
        try:
            error_msg = f"文件树加载失败: {str(error)}"
            self.logger.error(error_msg)

            # 更新状态栏显示错误
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.update_progress(0, error_msg)
                self.status_bar.disable_stop_button()

            # 重置加载状态
            self.file_tree_loading = False

        except Exception as ui_error:
            self.logger.error(f"显示错误信息失败: {ui_error}")

    def _safe_parse_font(self, font_str):
        """安全解析字体配置"""
        try:
            from tkinter import font as tkfont

            # 方法1: 尝试直接解析字体名称
            if isinstance(font_str, str):
                try:
                    label_font = tkfont.nametofont(font_str)
                    font_family = label_font.actual("family")
                    font_size = abs(label_font.actual("size"))
                    self.logger.info(f"成功解析字体: ({font_family}, {font_size})")
                    return (font_family, font_size)
                except tk.TclError:
                    pass

            # 方法2: 尝试解析字体元组
            if isinstance(font_str, (tuple, list)) and len(font_str) >= 2:
                font_family = font_str[0]
                font_size = int(font_str[1])
                self.logger.info(f"从元组解析字体: ({font_family}, {font_size})")
                return (font_family, font_size)

            # 方法3: 尝试从样式配置获取
            try:
                default_font = self.style.lookup("TLabel", "font")
                if default_font and isinstance(default_font, (tuple, list)):
                    font_family = default_font[0]
                    font_size = int(default_font[1])
                    self.logger.info(f"从样式配置解析字体: ({font_family}, {font_size})")
                    return (font_family, font_size)
            except:
                pass

            # 方法4: 使用系统默认字体
            try:
                system_font = tkfont.nametofont("TkDefaultFont")
                font_family = system_font.actual("family")
                font_size = abs(system_font.actual("size"))
                self.logger.info(f"使用系统默认字体: ({font_family}, {font_size})")
                return (font_family, font_size)
            except:
                pass

            # 最后的备用方案
            self.logger.warning("所有字体解析方法都失败，使用最基本的默认字体")
            return ("Arial", 10)

        except Exception as e:
            self.logger.warning(f"字体解析完全失败: {e}，使用最基本的默认字体")
            return ("Arial", 10)

    async def _load_files_from_database_async(self, interrupt_event=None, task_id=None):
        """异步加载文件树的核心方法"""
        try:
            if interrupt_event and interrupt_event.is_set():
                self.logger.info("异步文件树加载任务启动时被中断")
                if task_id:
                    self.progress_manager.cancel_task(task_id)
                return

            # 4. 在异步流程中通过task_id更新进度
            if task_id:
                self.progress_manager.update_progress(task_id, progress=5, status_message="从数据库获取数据...")

            files_data = await self._get_all_files_async(interrupt_event)

            if files_data is None:
                if not interrupt_event or not interrupt_event.is_set():
                    self.logger.warning("数据库中没有文件数据或获取失败")
                # 正确处理任务取消
                if interrupt_event and interrupt_event.is_set():
                    self.progress_manager.cancel_task(task_id, "任务被中断")
                else:
                    self.progress_manager.complete_task(task_id, success=False, message="数据库为空或查询失败")

                def update_ui_on_no_data():
                    if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                        self.file_tree_panel.update_file_tree({'files': {}, 'directory': '', 'file_count': 0, 'video_count': 0, 'junk_count': 0, 'whitelist_count': 0})
                    if hasattr(self, 'status_bar') and self.status_bar:
                        self.status_bar.update_progress(100, "数据库为空")
                        self.status_bar.disable_stop_button()

                self.root.after(0, update_ui_on_no_data)
                return

            # 处理空数据库的情况（数据库查询成功但返回空列表）
            if isinstance(files_data, list) and len(files_data) == 0:
                self.logger.info("数据库为空，清空文件树显示")
                if task_id:
                    self.progress_manager.complete_task(task_id, success=True, message="数据库为空，文件树已清空")

                def update_ui_on_empty_db():
                    if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                        self.file_tree_panel.update_file_tree({'files': {}, 'directory': '', 'file_count': 0, 'video_count': 0, 'junk_count': 0, 'whitelist_count': 0})
                        self.logger.info("文件树已清空显示")
                    if hasattr(self, 'status_bar') and self.status_bar:
                        self.status_bar.update_progress(100, "数据库为空，文件树已清空")
                        self.status_bar.disable_stop_button()

                self.root.after(0, update_ui_on_empty_db)
                return

            if task_id:
                self.progress_manager.update_progress(task_id, progress=20, status_message="处理文件数据...")

            processed_data = await self._process_files_data_async(files_data, interrupt_event, task_id)

            if processed_data is None:
                if task_id and interrupt_event and interrupt_event.is_set():
                    self.progress_manager.cancel_task(task_id)
                return

            def update_ui_on_success():
                try:
                    if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                        self.file_tree_panel.update_file_tree(processed_data)
                        self.logger.info("异步文件树界面更新完成")

                    if hasattr(self, 'status_bar') and self.status_bar:
                        self.status_bar.update_progress(100, "文件树加载完成")
                        self.status_bar.disable_stop_button()

                    # 更新文件统计信息 - 这是关键的缺失步骤！
                    self._update_file_stats(processed_data)

                    if task_id:
                        self.progress_manager.complete_task(task_id, message="加载成功")

                    self.file_tree_loading = False
                    self.logger.info("异步文件树加载完全完成，程序继续运行...")

                except Exception as ui_error:
                    self.logger.error(f"异步UI更新失败: {ui_error}")
                    if task_id:
                        self.progress_manager.complete_task(task_id, success=False, message=f"UI更新失败: {ui_error}")
            
            self.root.after(0, update_ui_on_success)

        except asyncio.CancelledError:
            self.logger.warning("文件树加载任务被取消")
            if task_id:
                self.progress_manager.cancel_task(task_id)
        except Exception as e:
            # 确保在这里也能捕获异常并更新UI
            error_message = str(e)
            if task_id:
                self.progress_manager.fail_task(task_id, error_message)
            # 使用默认参数来正确绑定变量
            self.root.after(0, lambda error=e: self._show_load_error_on_ui(error))
        finally:
            if interrupt_event and interrupt_event.is_set():
                if task_id:
                    self.progress_manager.cancel_task(task_id)

    async def _get_all_files_async(self, interrupt_event=None):
        """异步从数据库获取所有文件数据"""
        if not self.db_manager:
            self.logger.warning("数据库管理器未初始化，无法获取文件数据")
            return None

        self.logger.info("开始异步获取文件数据...")
        loop = asyncio.get_event_loop()
        try:
            # 添加超时机制，避免无限等待
            self.logger.info("调用数据库管理器获取所有文件...")

            # 检查数据库连接状态
            if hasattr(self.db_manager, 'is_connected') and not self.db_manager.is_connected():
                self.logger.error("数据库连接已断开")
                return None

            all_files = await asyncio.wait_for(
                loop.run_in_executor(None, self.db_manager.get_all_files),
                timeout=10.0  # 10秒超时，对本地数据库足够
            )
            self.logger.info(f"数据库查询完成，获取到 {len(all_files) if all_files else 0} 条记录")

            if not all_files:
                self.logger.warning("数据库查询返回空结果")
                # 返回空列表而不是None，这样文件树会被正确清空
                return []

            # 直接返回原始数据，不在这里处理
            return all_files

        except asyncio.TimeoutError:
            self.logger.error("数据库查询超时（30秒），可能数据库连接有问题")
            return None
        except Exception as e:
            self.logger.error(f"异步获取文件数据失败: {e}", exc_info=True)
            return None

    async def _process_files_data_async(self, files_data: List[Dict], interrupt_event=None, task_id=None) -> Optional[Dict[str, Any]]:
        """异步处理文件数据，构建文件树结构"""
        files_dict = {}
        video_count = 0
        junk_count = 0
        whitelist_count = 0
        total_files = len(files_data)

        # 异步处理循环
        for i, f in enumerate(files_data):
            if interrupt_event and interrupt_event.is_set():
                self.logger.warning("文件数据处理被中断")
                return None

            path = f.get("path", "")
            if not path:
                continue

            files_dict[path] = f
            if f.get("is_video"):
                video_count += 1
            if f.get("is_junk"):
                junk_count += 1
            if f.get("is_whitelist"):
                whitelist_count += 1
            
            # 每处理500个文件更新一次进度，并短暂让出控制权
            if i % 500 == 0:
                if task_id:
                    # 进度从20%增长到100%
                    progress = 20 + (i / total_files) * 80
                    self.progress_manager.update_progress(
                        task_id,
                        progress=progress,
                        status_message="构建文件树...",
                        current_item=os.path.basename(path)
                    )
                await asyncio.sleep(0.01)  # 适度让出CPU，避免过于频繁

        return {
            'files': files_dict,
            'directory': '',
            'file_count': total_files,
            'video_count': video_count,
            'junk_count': junk_count,
            'whitelist_count': whitelist_count
        }

    def load_files_from_directories(self, directories):
        """从指定目录加载文件并更新文件树"""
        try:
            if not self.db_manager:
                self.logger.error("数据库管理器未初始化，无法加载文件")
                return
            self.logger.info(f"开始从指定目录加载文件: {directories}")
            # 从数据库获取指定目录的文件
            files_data = self.db_manager.get_files_from_multiple_directories(directories)
            if not files_data:
                self.logger.warning("指定目录中没有文件数据")
                return
            # 转换为文件树需要的格式，强制保留path和file_id字段
            files_dict = {}
            video_count = 0
            junk_count = 0
            whitelist_count = 0
            for file_data in files_data:
                file_path = file_data.get('path', '')
                if file_path:
                    files_dict[file_path] = dict(file_data)  # 直接复制所有字段，确保path和file_id不丢失
                    # 统计文件类型
                    if file_data.get('is_video', False):
                        video_count += 1
                    if file_data.get('is_junk', False):
                        junk_count += 1
                    if file_data.get('is_whitelist', False):
                        whitelist_count += 1
            # 更新文件树
            if hasattr(self, 'file_tree_panel') and self.file_tree_panel:
                self.file_tree_panel.update_file_tree({
                    'files': files_dict,
                    'directory': directories[0] if len(directories) == 1 else '',
                    'directories': directories,
                    'file_count': len(files_dict),
                    'video_count': video_count,
                    'junk_count': junk_count,
                    'whitelist_count': whitelist_count
                })
            self.logger.info(f"成功从指定目录加载 {len(files_dict)} 个文件")
        except Exception as e:
            error_msg = f"从指定目录加载文件失败: {e}"
            self.logger.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def apply_font_size(self, font_size: int) -> None:
        """
        应用全局字体大小到所有UI控件
        """
        font = ("Helvetica", font_size)
        # 递归设置所有控件字体
        if hasattr(self, 'main_frame') and self.main_frame:
            if hasattr(self.ui_factory, 'apply_font_to_widget'):
                self.ui_factory.apply_font_to_widget(self.main_frame, font)
        # 其他自定义控件可单独处理
        if hasattr(self, 'status_panel') and self.status_panel:
            if hasattr(self.status_panel, 'set_font'):
                self.status_panel.set_font(font)

    # 删除之前的滚轮处理方法，使用直接绑定到控件的方式

    def _show_load_error_on_ui(self, error_exception):
        """在UI线程安全地显示加载错误"""
        error_message = str(error_exception)
        self.logger.error(f"异步加载文件树时发生严重错误: {error_message}", exc_info=True)
        self.file_tree_loading = False
        if hasattr(self, 'status_bar') and self.status_bar:
            self.status_bar.update_progress(0, "文件树加载失败")
            self.status_bar.disable_stop_button()
        messagebox.showerror("错误", f"异步加载文件树失败: {error_message}")


# 测试代码
if __name__ == "__main__":
    # 不要直接在这里创建MainWindow实例
    # 请使用app.py中的SmartFileManagerApp来启动应用程序
    from src.app import main
    main()