#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据一致性管理器

提供事务管理、数据校验和一致性保障功能。

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import asyncio
import hashlib
import json
import threading
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set

from ..logger import get_logger
from ..unified_types import TaskStatus, TaskType

logger = get_logger("ConsistencyManager")


class TransactionState(Enum):
    """事务状态枚举"""

    PENDING = "pending"
    ACTIVE = "active"
    COMMITTED = "committed"
    ABORTED = "aborted"
    FAILED = "failed"


class IsolationLevel(Enum):
    """隔离级别枚举"""

    READ_UNCOMMITTED = "read_uncommitted"
    READ_COMMITTED = "read_committed"
    REPEATABLE_READ = "repeatable_read"
    SERIALIZABLE = "serializable"


@dataclass
class TransactionOperation:
    """事务操作"""

    operation_id: str
    operation_type: str  # CREATE, UPDATE, DELETE, READ
    resource_id: str
    data: Any
    timestamp: float = field(default_factory=time.time)
    rollback_data: Optional[Any] = None


@dataclass
class Transaction:
    """事务对象"""

    transaction_id: str
    state: TransactionState = TransactionState.PENDING
    isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED
    operations: List[TransactionOperation] = field(default_factory=list)
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    locks: Set[str] = field(default_factory=set)

    @property
    def duration(self) -> float:
        """事务持续时间"""
        end = self.end_time or time.time()
        return end - self.start_time


class ConsistencyManager:
    """
    数据一致性管理器

    提供事务管理、锁机制和数据一致性保障
    """

    def __init__(self):
        """初始化一致性管理器"""
        self.logger = get_logger(self.__class__.__name__)

        # 事务管理
        self._transactions: Dict[str, Transaction] = {}
        self._transaction_lock = asyncio.Lock()

        # 资源锁管理
        self._resource_locks: Dict[str, asyncio.Lock] = {}
        self._lock_owners: Dict[str, str] = {}  # resource_id -> transaction_id
        self._lock_waiters: Dict[
            str, List[str]
        ] = {}  # resource_id -> [transaction_ids]

        # 数据版本管理
        self._data_versions: Dict[
            str, Dict[str, Any]
        ] = {}  # resource_id -> {version: data}
        self._current_versions: Dict[str, str] = {}  # resource_id -> current_version

        # 一致性检查器
        self._consistency_checkers: Dict[str, Callable] = {}

        # 统计信息
        self._stats = {
            "total_transactions": 0,
            "committed_transactions": 0,
            "aborted_transactions": 0,
            "failed_transactions": 0,
            "deadlock_count": 0,
        }

        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()

        self.logger.info("数据一致性管理器初始化完成")

    def _start_cleanup_task(self):
        """启动清理任务"""

        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(300)  # 每5分钟清理一次
                    await self._cleanup_old_transactions()
                    await self._cleanup_old_versions()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"清理任务出错: {e}")

        self._cleanup_task = asyncio.create_task(cleanup_loop())

    async def begin_transaction(
        self,
        transaction_id: Optional[str] = None,
        isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED,
    ) -> str:
        """
        开始事务

        Args:
            transaction_id: 事务ID，如果为None则自动生成
            isolation_level: 隔离级别

        Returns:
            事务ID
        """
        if transaction_id is None:
            transaction_id = f"txn_{int(time.time() * 1000000)}"

        async with self._transaction_lock:
            if transaction_id in self._transactions:
                raise ValueError(f"事务ID已存在: {transaction_id}")

            transaction = Transaction(
                transaction_id=transaction_id,
                state=TransactionState.ACTIVE,
                isolation_level=isolation_level,
            )

            self._transactions[transaction_id] = transaction
            self._stats["total_transactions"] += 1

            self.logger.info(f"开始事务: {transaction_id}, 隔离级别: {isolation_level.value}")
            return transaction_id

    async def commit_transaction(self, transaction_id: str):
        """
        提交事务

        Args:
            transaction_id: 事务ID
        """
        async with self._transaction_lock:
            if transaction_id not in self._transactions:
                raise ValueError(f"事务不存在: {transaction_id}")

            transaction = self._transactions[transaction_id]

            if transaction.state != TransactionState.ACTIVE:
                raise ValueError(f"事务状态无效: {transaction.state}")

            try:
                # 执行一致性检查
                await self._validate_transaction_consistency(transaction)

                # 应用所有操作
                await self._apply_transaction_operations(transaction)

                # 更新事务状态
                transaction.state = TransactionState.COMMITTED
                transaction.end_time = time.time()

                # 释放锁
                await self._release_transaction_locks(transaction_id)

                self._stats["committed_transactions"] += 1
                self.logger.info(
                    f"事务提交成功: {transaction_id}, 耗时: {transaction.duration:.3f}s"
                )

            except Exception as e:
                # 提交失败，回滚事务
                self.logger.error(f"事务提交失败: {transaction_id}, 错误: {e}")
                await self.abort_transaction(transaction_id)
                raise

    async def abort_transaction(self, transaction_id: str):
        """
        中止事务

        Args:
            transaction_id: 事务ID
        """
        async with self._transaction_lock:
            if transaction_id not in self._transactions:
                self.logger.warning(f"尝试中止不存在的事务: {transaction_id}")
                return

            transaction = self._transactions[transaction_id]

            try:
                # 回滚操作
                await self._rollback_transaction_operations(transaction)

                # 更新事务状态
                transaction.state = TransactionState.ABORTED
                transaction.end_time = time.time()

                # 释放锁
                await self._release_transaction_locks(transaction_id)

                self._stats["aborted_transactions"] += 1
                self.logger.info(
                    f"事务中止: {transaction_id}, 耗时: {transaction.duration:.3f}s"
                )

            except Exception as e:
                transaction.state = TransactionState.FAILED
                self._stats["failed_transactions"] += 1
                self.logger.error(f"事务中止失败: {transaction_id}, 错误: {e}")
                raise

    @asynccontextmanager
    async def transaction(
        self,
        transaction_id: Optional[str] = None,
        isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED,
    ):
        """
        事务上下文管理器

        Args:
            transaction_id: 事务ID
            isolation_level: 隔离级别
        """
        txn_id = await self.begin_transaction(transaction_id, isolation_level)
        try:
            yield txn_id
            await self.commit_transaction(txn_id)
        except Exception as e:
            await self.abort_transaction(txn_id)
            raise

    async def acquire_lock(
        self, transaction_id: str, resource_id: str, timeout: float = 30.0
    ) -> bool:
        """
        获取资源锁

        Args:
            transaction_id: 事务ID
            resource_id: 资源ID
            timeout: 超时时间

        Returns:
            是否成功获取锁
        """
        if transaction_id not in self._transactions:
            raise ValueError(f"事务不存在: {transaction_id}")

        # 检查是否已经持有锁
        if (
            resource_id in self._lock_owners
            and self._lock_owners[resource_id] == transaction_id
        ):
            return True

        # 创建资源锁（如果不存在）
        if resource_id not in self._resource_locks:
            self._resource_locks[resource_id] = asyncio.Lock()

        try:
            # 尝试获取锁
            acquired = await asyncio.wait_for(
                self._resource_locks[resource_id].acquire(), timeout=timeout
            )

            if acquired:
                self._lock_owners[resource_id] = transaction_id
                self._transactions[transaction_id].locks.add(resource_id)
                self.logger.debug(f"获取锁成功: {transaction_id} -> {resource_id}")
                return True

        except asyncio.TimeoutError:
            self.logger.warning(f"获取锁超时: {transaction_id} -> {resource_id}")
            return False

        return False

    async def release_lock(self, transaction_id: str, resource_id: str):
        """
        释放资源锁

        Args:
            transaction_id: 事务ID
            resource_id: 资源ID
        """
        if (
            resource_id in self._lock_owners
            and self._lock_owners[resource_id] == transaction_id
        ):
            del self._lock_owners[resource_id]

            if transaction_id in self._transactions:
                self._transactions[transaction_id].locks.discard(resource_id)

            if resource_id in self._resource_locks:
                self._resource_locks[resource_id].release()

            self.logger.debug(f"释放锁: {transaction_id} -> {resource_id}")

    async def _release_transaction_locks(self, transaction_id: str):
        """释放事务的所有锁"""
        if transaction_id not in self._transactions:
            return

        transaction = self._transactions[transaction_id]
        locks_to_release = list(transaction.locks)

        for resource_id in locks_to_release:
            await self.release_lock(transaction_id, resource_id)

    async def read_data(self, transaction_id: str, resource_id: str) -> Any:
        """
        读取数据

        Args:
            transaction_id: 事务ID
            resource_id: 资源ID

        Returns:
            数据内容
        """
        transaction = self._transactions.get(transaction_id)
        if not transaction:
            raise ValueError(f"事务不存在: {transaction_id}")

        # 根据隔离级别决定读取策略
        if transaction.isolation_level == IsolationLevel.READ_UNCOMMITTED:
            # 读取最新版本（可能是未提交的）
            return await self._read_latest_version(resource_id)

        elif transaction.isolation_level == IsolationLevel.READ_COMMITTED:
            # 读取已提交的最新版本
            return await self._read_committed_version(resource_id)

        elif transaction.isolation_level in [
            IsolationLevel.REPEATABLE_READ,
            IsolationLevel.SERIALIZABLE,
        ]:
            # 可重复读，需要获取锁
            if not await self.acquire_lock(transaction_id, resource_id):
                raise RuntimeError(f"无法获取读锁: {resource_id}")
            return await self._read_committed_version(resource_id)

        else:
            raise ValueError(f"不支持的隔离级别: {transaction.isolation_level}")

    async def write_data(
        self,
        transaction_id: str,
        resource_id: str,
        data: Any,
        operation_type: str = "UPDATE",
    ) -> str:
        """
        写入数据

        Args:
            transaction_id: 事务ID
            resource_id: 资源ID
            data: 数据内容
            operation_type: 操作类型

        Returns:
            操作ID
        """
        transaction = self._transactions.get(transaction_id)
        if not transaction:
            raise ValueError(f"事务不存在: {transaction_id}")

        # 获取写锁
        if not await self.acquire_lock(transaction_id, resource_id):
            raise RuntimeError(f"无法获取写锁: {resource_id}")

        # 创建操作记录
        operation_id = f"op_{int(time.time() * 1000000)}"

        # 保存回滚数据
        rollback_data = None
        if operation_type in ["UPDATE", "DELETE"]:
            rollback_data = await self._read_committed_version(resource_id)

        operation = TransactionOperation(
            operation_id=operation_id,
            operation_type=operation_type,
            resource_id=resource_id,
            data=data,
            rollback_data=rollback_data,
        )

        transaction.operations.append(operation)

        self.logger.debug(
            f"记录写操作: {transaction_id} -> {resource_id} ({operation_type})"
        )
        return operation_id

    async def _read_latest_version(self, resource_id: str) -> Any:
        """读取最新版本数据"""
        if resource_id not in self._current_versions:
            return None

        current_version = self._current_versions[resource_id]
        return self._data_versions.get(resource_id, {}).get(current_version)

    async def _read_committed_version(self, resource_id: str) -> Any:
        """读取已提交的版本数据"""
        # 简化实现：直接返回当前版本
        # 实际实现中需要区分已提交和未提交的版本
        return await self._read_latest_version(resource_id)

    async def _apply_transaction_operations(self, transaction: Transaction):
        """应用事务操作"""
        for operation in transaction.operations:
            await self._apply_operation(operation)

    async def _apply_operation(self, operation: TransactionOperation):
        """应用单个操作"""
        resource_id = operation.resource_id

        # 生成新版本ID
        version_id = f"v_{int(time.time() * 1000000)}"

        # 初始化资源版本存储
        if resource_id not in self._data_versions:
            self._data_versions[resource_id] = {}

        # 存储数据版本
        if operation.operation_type == "DELETE":
            self._data_versions[resource_id][version_id] = None
        else:
            self._data_versions[resource_id][version_id] = operation.data

        # 更新当前版本
        self._current_versions[resource_id] = version_id

        self.logger.debug(
            f"应用操作: {operation.operation_type} {resource_id} -> {version_id}"
        )

    async def _rollback_transaction_operations(self, transaction: Transaction):
        """回滚事务操作"""
        # 按相反顺序回滚操作
        for operation in reversed(transaction.operations):
            await self._rollback_operation(operation)

    async def _rollback_operation(self, operation: TransactionOperation):
        """回滚单个操作"""
        resource_id = operation.resource_id

        if operation.rollback_data is not None:
            # 恢复到回滚数据
            version_id = f"rollback_{int(time.time() * 1000000)}"

            if resource_id not in self._data_versions:
                self._data_versions[resource_id] = {}

            self._data_versions[resource_id][version_id] = operation.rollback_data
            self._current_versions[resource_id] = version_id

        elif operation.operation_type == "CREATE":
            # 删除创建的数据
            if resource_id in self._current_versions:
                del self._current_versions[resource_id]

        self.logger.debug(f"回滚操作: {operation.operation_type} {resource_id}")

    async def _validate_transaction_consistency(self, transaction: Transaction):
        """验证事务一致性"""
        for operation in transaction.operations:
            resource_id = operation.resource_id

            # 执行注册的一致性检查器
            if resource_id in self._consistency_checkers:
                checker = self._consistency_checkers[resource_id]
                try:
                    if asyncio.iscoroutinefunction(checker):
                        await checker(operation.data)
                    else:
                        checker(operation.data)
                except Exception as e:
                    raise ValueError(f"一致性检查失败: {resource_id}, 错误: {e}")

    def register_consistency_checker(self, resource_id: str, checker: Callable):
        """注册一致性检查器"""
        self._consistency_checkers[resource_id] = checker
        self.logger.debug(f"注册一致性检查器: {resource_id}")

    async def _cleanup_old_transactions(self):
        """清理旧事务"""
        current_time = time.time()
        cleanup_threshold = current_time - 3600  # 1小时前的事务

        to_remove = []
        for txn_id, transaction in self._transactions.items():
            if (
                transaction.state
                in [
                    TransactionState.COMMITTED,
                    TransactionState.ABORTED,
                    TransactionState.FAILED,
                ]
                and transaction.end_time
                and transaction.end_time < cleanup_threshold
            ):
                to_remove.append(txn_id)

        for txn_id in to_remove:
            del self._transactions[txn_id]

        if to_remove:
            self.logger.info(f"清理旧事务: {len(to_remove)}个")

    async def _cleanup_old_versions(self):
        """清理旧版本数据"""
        # 保留最近的10个版本
        for resource_id, versions in self._data_versions.items():
            if len(versions) > 10:
                # 按时间戳排序，保留最新的10个
                sorted_versions = sorted(
                    versions.keys(),
                    key=lambda v: int(v.split("_")[1]) if "_" in v else 0,
                )
                to_remove = sorted_versions[:-10]

                for version in to_remove:
                    del versions[version]

                if to_remove:
                    self.logger.debug(f"清理旧版本: {resource_id}, 删除 {len(to_remove)}个版本")

    def get_transaction_status(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """获取事务状态"""
        transaction = self._transactions.get(transaction_id)
        if not transaction:
            return None

        return {
            "transaction_id": transaction.transaction_id,
            "state": transaction.state.value,
            "isolation_level": transaction.isolation_level.value,
            "operation_count": len(transaction.operations),
            "lock_count": len(transaction.locks),
            "duration": transaction.duration,
            "start_time": transaction.start_time,
            "end_time": transaction.end_time,
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_transactions = len(
            [
                t
                for t in self._transactions.values()
                if t.state == TransactionState.ACTIVE
            ]
        )

        return {
            **self._stats,
            "active_transactions": active_transactions,
            "total_locks": len(self._lock_owners),
            "total_resources": len(self._data_versions),
        }


# 全局一致性管理器实例
_consistency_manager = None


def get_consistency_manager() -> ConsistencyManager:
    """获取全局一致性管理器实例"""
    global _consistency_manager
    if _consistency_manager is None:
        _consistency_manager = ConsistencyManager()
    return _consistency_manager
