#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件树面板模块

该模块负责创建和管理文件树面板, 包括:
1. 文件树的创建和更新
2. 文件树的事件处理
3. 文件树的上下文菜单
4. 文件树的过滤和搜索功能

作者: AI助手
日期: 2023-06-01
版本: 1.0.0
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import queue
import time
from pathlib import Path
import re
import logging
from typing import Dict, List, Optional, Set, Tuple, Any
from src.utils.refresh_dedup_guard import RefreshDedupGuard
from src.utils.dynamic_window_calculator import DynamicWindowCalculator
from src.utils.debounce_config_manager import DebounceConfigManager
from src.utils.logger import get_logger
# 导入自定义模块
from src.core.dependency_injection import resolve
from src.utils.format_utils import format_size, _normalize_path

logger = get_logger(__name__)

class FileTreePanel:
    """
    文件树面板类
    
    创建和管理文件树面板
    """
    def __init__(self, parent, main_window, logger: Any):
        """
        初始化文件树面板
        
        参数:
            parent: 父容器
            main_window: 主窗口实例
            logger: 日志记录器
        """
        self.parent = parent
        self.main_window = main_window
        self.logger = logger
        
        # 创建框架 - 使用ttk.LabelFrame并设置padding
        self.frame = ttk.LabelFrame(parent, text="文件浏览器", padding=10)
        
        # 创建搜索框架
        self.create_search_frame()
        
        # 创建文件树
        self.create_file_tree()
        
        # 创建上下文菜单
        self.create_context_menu()
        
        # 绑定事件
        self.bind_events()

        # 初始化内存管理器
        self._init_memory_manager()

        # 初始化变量
        self.files = {}  # 文件信息字典
        self.current_directory = ""  # 当前目录
        self.filter_text = ""  # 过滤文本
        self.sort_column = "name"  # 排序列
        self.sort_reverse = False  # 排序方向
        
        # ----------- 新增：增量刷新与懒加载相关变量 -----------
        self.visible_items = set()  # 当前可见的项目ID集合
        self.pending_whitelist_refresh = set()  # 待刷新白名单状态的文件ID集合
        self.lazy_refresh_scheduled = False  # 是否已调度懒加载刷新
        self.last_whitelist_rules_hash = None  # 上次白名单规则的哈希值
        self.affected_files_cache = {}  # 受影响的文件缓存 {rule_hash: set(file_ids)}
        
        # ----------- 新增：缓存机制与并发优化相关变量 -----------
        self.whitelist_cache = {}  # 白名单判断结果缓存 {file_path: (is_whitelist, timestamp)}
        self.cache_ttl = 300  # 缓存有效期（秒）
        self.concurrent_refresh_enabled = True  # 是否启用并发刷新
        self.max_concurrent_workers = 4  # 最大并发工作线程数
        self.refresh_lock = threading.Lock()  # 刷新锁，确保线程安全

        # ----------- 新增：白名单检查控制 -----------
        self.auto_whitelist_check = False  # 是否自动进行白名单检查（默认关闭）
        self.manual_whitelist_check = True  # 是否支持手动白名单检查

        # ----------- 新增：文件树构建状态 -----------
        self.tree_building_state = None  # 文件树构建状态

        # ----------- 新增：内存文件树管理 -----------
        self.memory_manager = None  # 内存文件树管理器
        self.virtualized_renderer = None  # 虚拟化渲染器
        self.use_memory_mode = True  # 是否使用内存模式（默认启用）
        self.memory_loading = False  # 内存加载状态

        # ----------- 新增：重复文件夹检测优化 -----------
        self.folder_name_cache = {}  # 文件夹名称缓存 {parent_path: {folder_names}}
        self.duplicate_stats = {}  # 重复统计 {folder_name: count}
        self.duplicate_details = {}  # 重复详情 {folder_name: [paths]}
        self.last_duplicate_log_time = {}  # 最后日志时间 {folder_name: timestamp}
        
        # ----------- 新增：UI体验提升相关变量 -----------
        self.refresh_progress = None  # 刷新进度条
        self.refresh_cancelled = False  # 是否取消刷新
        self.refresh_status_label = None  # 刷新状态标签
        self.refresh_cancel_button = None  # 取消刷新按钮
        self.refresh_in_progress = False  # 是否正在刷新
        
        # ----------- 新增：懒加载相关变量 -----------
        self.lazy_loading_enabled = True  # 是否启用懒加载
        self.lazy_loaded_folders = set()  # 已懒加载的文件夹路径集合
        self.folder_children_cache = {}  # 文件夹子项缓存 {folder_path: [children]}
        self.lazy_loading_queue = queue.Queue()  # 懒加载队列
        self.lazy_loading_thread = None  # 懒加载线程
        self.lazy_loading_running = False  # 懒加载线程运行状态
        self.folder_placeholder_items = {}  # 文件夹占位符项目 {folder_id: placeholder_id}
        self.expanding_folders = set()  # 正在展开的文件夹集合

        # ----------- 新增：性能优化相关变量 -----------
        self.file_index_by_parent = {}  # 按父目录索引的文件 {parent_path: [files]}
        self.file_index_built = False  # 文件索引是否已构建
        
        # 订阅白名单规则变更事件
        self._subscribe_whitelist_events()

        # 初始化时更新一次统计信息
        if hasattr(self, 'main_window') and hasattr(self.main_window, 'root'):
            self.main_window.root.after(500, self.calculate_file_stats)  # 延迟一点执行，确保UI已经加载

    def _init_memory_manager(self):
        """初始化内存文件树管理器"""
        try:
            # 确保属性已初始化
            if not hasattr(self, 'use_memory_mode'):
                self.use_memory_mode = True
                self.logger.info("[文件树] 初始化 use_memory_mode 属性")

            if self.use_memory_mode and hasattr(self.main_window, 'db_manager') and self.main_window.db_manager:
                from src.core.memory_file_tree import MemoryFileTreeManager

                # 创建内存管理器
                self.memory_manager = MemoryFileTreeManager(
                    self.main_window.db_manager,
                    self.logger
                )

                self.logger.info("[文件树] 内存文件树管理器已初始化")
            else:
                self.logger.info("[文件树] 使用传统模式，未启用内存管理器")

        except Exception as e:
            self.logger.error(f"[文件树] 初始化内存管理器失败: {e}")
            self.use_memory_mode = False

    def _start_memory_loading(self):
        """启动内存加载"""
        try:
            self.memory_loading = True
            self.logger.info("[文件树] 开始异步加载文件到内存...")

            # 显示加载状态
            self._show_loading_status("正在加载文件到内存...")

            # 启动异步加载
            import asyncio
            import threading

            def run_async_loading():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 定义进度回调
                    def progress_callback(percent, message):
                        if hasattr(self.main_window, 'root'):
                            self.main_window.root.after(0, lambda: self._update_loading_progress(percent, message))

                    # 执行异步加载
                    success = loop.run_until_complete(
                        self.memory_manager.load_to_memory(progress_callback)
                    )

                    # 加载完成回调
                    if hasattr(self.main_window, 'root'):
                        self.main_window.root.after(0, lambda: self._on_memory_loading_complete(success))

                except Exception as e:
                    self.logger.error(f"异步内存加载失败: {e}")
                    if hasattr(self.main_window, 'root'):
                        self.main_window.root.after(0, lambda: self._on_memory_loading_complete(False))
                finally:
                    loop.close()

            # 在后台线程中运行
            loading_thread = threading.Thread(target=run_async_loading, daemon=True)
            loading_thread.start()

        except Exception as e:
            self.logger.error(f"启动内存加载失败: {e}")
            self.memory_loading = False

    def _subscribe_whitelist_events(self):
        """订阅白名单规则变更事件"""
        try:
            from src.utils.event_system import get_event_system
            event_system = get_event_system()
            
            # 订阅白名单规则变更事件 - 直接使用整数优先级2（NORMAL）
            event_system.subscribe("whitelist_rules_changed", self._on_whitelist_rules_changed, 2, subscriber_id="file_tree_panel")
            
            self.logger.info("已订阅白名单规则变更事件")
        except Exception as e:
            self.logger.error(f"订阅白名单规则变更事件失败: {e}")

    def _on_whitelist_rules_changed(self, event_data=None):
        """
        白名单规则变更事件处理
        分析受影响的文件，只刷新这些文件的白名单状态
        """
        try:
            self.logger.info("收到白名单规则变更事件，开始增量刷新")
            
            # 获取当前白名单规则哈希
            current_rules_hash = self._get_whitelist_rules_hash()
            
            # 如果规则没有实际变更，跳过刷新
            if current_rules_hash == self.last_whitelist_rules_hash:
                self.logger.info("白名单规则哈希未变更，跳过刷新")
                return
            
            # 分析受影响的文件
            affected_files = self._analyze_affected_files(current_rules_hash)
            
            if affected_files:
                self.logger.info(f"检测到 {len(affected_files)} 个文件受白名单规则变更影响")
                # 将受影响的文件加入待刷新队列
                self.pending_whitelist_refresh.update(affected_files)
                # 调度增量刷新
                self._schedule_incremental_refresh()
            else:
                self.logger.info("未检测到受影响的文件")
            
            # 更新规则哈希
            self.last_whitelist_rules_hash = current_rules_hash
            
        except Exception as e:
            self.logger.error(f"处理白名单规则变更事件失败: {e}")
    
    def _get_whitelist_rules_hash(self):
        """获取当前白名单规则的哈希值"""
        try:
            file_scanner = None
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                file_scanner = self.main_window.file_scanner
            
            if file_scanner and hasattr(file_scanner, 'whitelist_patterns'):
                # 使用白名单模式的字符串表示作为哈希
                patterns_str = str(sorted(file_scanner.whitelist_patterns))
                return hash(patterns_str)
            return None
        except Exception as e:
            self.logger.error(f"获取白名单规则哈希失败: {e}")
            return None
    
    def _create_compatible_file_info(self, file_info_obj):
        """
        创建兼容的FileInfo对象，确保有path属性
        
        参数:
            file_info_obj: src.data.models.FileInfo对象
            
        返回:
            兼容的FileInfo对象，具有path属性
        """
        class CompatibleFileInfo:
            def __init__(self, file_info):
                # 优先使用file_path，如果没有则使用path
                if hasattr(file_info, 'file_path') and file_info.file_path:
                    self.path = file_info.file_path
                elif hasattr(file_info, 'path') and file_info.path:
                    self.path = file_info.path
                else:
                    # 如果都没有，记录错误并使用空字符串
                    self.path = ""
                    if hasattr(self, 'logger'):
                        self.logger.error(f"FileInfo对象缺少路径属性: {file_info}")
                
                # 安全地复制其他属性
                self.name = getattr(file_info, 'name', '')
                self.extension = getattr(file_info, 'extension', '')
                
                # 复制其他必要属性
                for attr in ['size', 'modified_time', 'created_time', 'is_video', 'is_junk', 'is_whitelist']:
                    if hasattr(file_info, attr):
                        setattr(self, attr, getattr(file_info, attr))
        
        return CompatibleFileInfo(file_info_obj)
    
    def _analyze_affected_files(self, current_rules_hash):
        """
        分析受白名单规则变更影响的文件
        返回受影响的文件ID集合
        """
        try:
            # 检查缓存
            if current_rules_hash in self.affected_files_cache:
                return self.affected_files_cache[current_rules_hash]
            
            affected_files = set()
            file_scanner = None
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                file_scanner = self.main_window.file_scanner
            
            if not file_scanner:
                return affected_files
            
            # 遍历所有文件，检查哪些文件的白名单状态可能发生变化
            for file_id, file_info in self.files.items():
                try:
                    from src.data.models import FileInfo
                    file_obj = FileInfo.from_dict(file_info)
                    
                    # 创建兼容的FileInfo对象
                    compatible_file_obj = self._create_compatible_file_info(file_obj)
                    
                    # 使用最新规则判断白名单状态
                    current_is_whitelist = file_scanner.is_whitelist_file(compatible_file_obj)
                    original_is_whitelist = file_info.get('is_whitelist', False)
                    
                    # 如果状态发生变化，加入受影响文件集合
                    if current_is_whitelist != original_is_whitelist:
                        affected_files.add(file_id)
                        # 同时更新文件信息
                        file_info['is_whitelist'] = current_is_whitelist
                        
                except Exception as e:
                    self.logger.error(f"分析文件 {file_id} 受影响状态失败: {e}")
            
            # 缓存结果
            self.affected_files_cache[current_rules_hash] = affected_files
            
            return affected_files
            
        except Exception as e:
            self.logger.error(f"分析受影响文件失败: {e}")
            return set()
    
    def _schedule_incremental_refresh(self):
        """调度增量刷新，优先刷新可见节点"""
        if self.lazy_refresh_scheduled:
            return
        
        self.lazy_refresh_scheduled = True
        
        def incremental_refresh():
            try:
                # 获取当前可见的项目
                self._update_visible_items()
                
                # 优先刷新可见的受影响文件
                visible_affected = self.pending_whitelist_refresh.intersection(self.visible_items)
                
                if visible_affected:
                    self.logger.info(f"刷新 {len(visible_affected)} 个可见受影响文件的白名单状态")
                    self._refresh_visible_whitelist_status(visible_affected)
                
                # 清空已刷新的文件
                self.pending_whitelist_refresh.difference_update(visible_affected)
                
                # 如果还有待刷新的文件，继续调度
                if self.pending_whitelist_refresh:
                    self.frame.after(100, incremental_refresh)  # 100ms后继续
                else:
                    self.lazy_refresh_scheduled = False
                    self.logger.info("增量刷新完成")
                    
            except Exception as e:
                self.logger.error(f"增量刷新失败: {e}")
                self.lazy_refresh_scheduled = False
        
        # 立即开始刷新
        self.frame.after(10, incremental_refresh)
    
    def _update_visible_items(self):
        """更新当前可见的项目ID集合"""
        try:
            self.visible_items.clear()
            
            # 获取当前可见的项目
            visible_items = self.tree.get_children()
            for item in visible_items:
                try:
                    item_values = self.tree.item(item, 'values')
                    # 检查values是否为空或长度不足
                    if not item_values or len(item_values) == 0:
                        self.logger.debug(f"跳过空values的项目: {item}")
                        continue
                    
                    # 检查是否有足够的列
                    if len(item_values) >= 6:
                        # 使用file_id作为唯一标识（第7列，索引6）
                        file_id = item_values[6]
                        if file_id and file_id != '(加载中...)':
                            self.visible_items.add(file_id)
                    elif len(item_values) >= 5:
                        # 兼容旧版本：使用路径作为唯一标识（第5列，索引4）
                        file_path = item_values[4]
                        if file_path and file_path != '(加载中...)':
                            self.visible_items.add(file_path)
                    else:
                        self.logger.debug(f"跳过values长度不足的项目: {item}, values: {item_values}")
                        
                except Exception as item_error:
                    self.logger.debug(f"处理单个项目时出错: {item_error}, item: {item}")
                    continue
                
        except Exception as e:
            self.logger.error(f"更新可见项目失败: {e}")
    
    def _refresh_visible_whitelist_status(self, file_identifiers):
        """刷新指定文件的白名单状态（仅UI更新）"""
        try:
            for file_identifier in file_identifiers:
                # 支持file_id和path两种标识符
                file_info = None
                if file_identifier in self.files:
                    # 直接匹配（file_id或path）
                    file_info = self.files[file_identifier]
                else:
                    # 通过path查找
                    for path, info in self.files.items():
                        if info.get('file_id') == file_identifier:
                            file_info = info
                            break
                
                if file_info:
                    is_whitelist = file_info.get('is_whitelist', False)
                    # 更新树形视图中的显示
                    self._update_tree_item_whitelist_status(file_identifier, is_whitelist)
                    
        except Exception as e:
            self.logger.error(f"刷新可见文件白名单状态失败: {e}")
    
    def _update_tree_item_whitelist_status(self, file_identifier, is_whitelist):
        """更新树形视图中指定项目的白名单状态显示"""
        try:
            # 查找对应的树形项目
            for item in self.tree.get_children():
                try:
                    item_values = self.tree.item(item, 'values')
                    if not item_values or len(item_values) < 5:
                        continue
                    
                    # 优先使用file_id匹配（第7列，索引6），如果没有则使用路径匹配（第5列，索引4）
                    if len(item_values) >= 7 and item_values[6] == file_identifier:
                        # 使用file_id匹配
                        current_values = list(item_values)
                        if len(current_values) > 5:
                            current_values[5] = "✓" if is_whitelist else ""
                            self.tree.item(item, values=current_values)
                        break
                    elif len(item_values) >= 5 and item_values[4] == file_identifier:
                        # 使用路径匹配（兼容旧版本）
                        current_values = list(item_values)
                        if len(current_values) > 5:
                            current_values[5] = "✓" if is_whitelist else ""
                            self.tree.item(item, values=current_values)
                        break
                        
                except Exception as item_error:
                    self.logger.debug(f"处理单个项目白名单状态时出错: {item_error}, item: {item}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"更新树形项目白名单状态失败: {e}")
    
    def _on_tree_scroll(self, event=None):
        """树形视图滚动事件处理，触发懒加载刷新"""
        try:
            # 更新可见项目
            self._update_visible_items()
            
            # 检查是否有待刷新的可见文件
            visible_pending = self.pending_whitelist_refresh.intersection(self.visible_items)
            if visible_pending and not self.lazy_refresh_scheduled:
                self.logger.info(f"滚动检测到 {len(visible_pending)} 个待刷新可见文件，触发懒加载")
                self._schedule_incremental_refresh()
                
        except Exception as e:
            self.logger.error(f"处理树形视图滚动事件失败: {e}")
    
    def _on_tree_expand(self, event=None):
        """树形视图展开事件处理，触发懒加载刷新"""
        try:
            # 更新可见项目
            self._update_visible_items()
            
            # 检查是否有待刷新的可见文件
            visible_pending = self.pending_whitelist_refresh.intersection(self.visible_items)
            if visible_pending and not self.lazy_refresh_scheduled:
                self.logger.info(f"展开检测到 {len(visible_pending)} 个待刷新可见文件，触发懒加载")
                self._schedule_incremental_refresh()
                
        except Exception as e:
            self.logger.error(f"处理树形视图展开事件失败: {e}")
    
    def _on_whitelist_refresh_complete(self):
        """白名单状态刷新完成回调"""
        try:
            # 只刷新文件树内容，不再刷新统计标签
            self.clear_tree()
            self.populate_tree(self.files)
            self.logger.info("白名单状态刷新完成")
        except Exception as e:
            self.logger.error(f"白名单状态刷新完成回调执行失败: {e}")
    
    def get_frame(self):
        return self.frame
    
    def create_search_frame(self):
        """创建搜索框架"""
        # 顶部统计信息label，背景色浅黄色
        self.top_stats_label = ttk.Label(self.frame, text="文件数: 0  视频文件: 0  垃圾文件: 0  白名单文件: 0", anchor="w", background="#fff9db")
        self.top_stats_label.grid(row=0, column=0, sticky="ew", padx=5, pady=(5, 0))
        
        # 搜索框架 - 使用ttk.Frame并设置padding
        search_frame = ttk.Frame(self.frame, padding=5)
        search_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # 配置search_frame的grid权重
        search_frame.grid_columnconfigure(1, weight=1)  # 搜索输入框列扩展
        
        # 搜索标签
        ttk.Label(search_frame, text="搜索:").grid(row=0, column=0, padx=(0, 5), sticky="w")
        
        # 搜索变量
        self.search_var = tk.StringVar()
        
        # 搜索输入框
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.grid(row=0, column=1, sticky="ew")
        
        # 搜索按钮
        self.search_button = ttk.Button(search_frame, text="搜索", command=self.filter_tree, width=8)
        self.search_button.grid(row=0, column=2, padx=(5, 0), sticky="e")
        
        # 刷新按钮
        self.refresh_button = ttk.Button(search_frame, text="刷新", command=self.refresh_file_tree, width=8)
        self.refresh_button.grid(row=0, column=3, padx=(5, 0), sticky="e")
        
        # 清除按钮
        self.clear_button = ttk.Button(search_frame, text="清除", command=self.clear_filter, width=8)
        self.clear_button.grid(row=0, column=4, padx=(5, 0), sticky="e")

        # 手动白名单检查按钮
        self.whitelist_button = ttk.Button(search_frame, text="检查白名单", command=self.manual_check_whitelist, width=10)
        self.whitelist_button.grid(row=0, column=5, padx=(5, 0), sticky="e")
        
        # ----------- 新增：刷新状态显示区域 -----------
        refresh_frame = ttk.Frame(self.frame, padding=5)
        refresh_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=(0, 5))
        
        # 配置refresh_frame的grid权重
        refresh_frame.grid_columnconfigure(1, weight=1)  # 进度条列扩展
        
        # 刷新状态标签
        self.refresh_status_label = ttk.Label(refresh_frame, text="就绪", foreground="green")
        self.refresh_status_label.grid(row=0, column=0, sticky="w")
        
        # 刷新进度条
        self.refresh_progress = ttk.Progressbar(refresh_frame, mode='indeterminate', length=200)
        self.refresh_progress.grid(row=0, column=1, sticky="ew", padx=(10, 5))
        
        # 取消刷新按钮
        self.refresh_cancel_button = ttk.Button(refresh_frame, text="取消刷新", 
                                              command=self._cancel_refresh, state='disabled')
        self.refresh_cancel_button.grid(row=0, column=2, sticky="e")
        
        # 过滤选项框架 - 使用ttk.Frame并设置padding
        filter_frame = ttk.Frame(self.frame, padding=5)
        filter_frame.grid(row=3, column=0, sticky="ew", padx=5, pady=(0, 5))
        
        # 配置filter_frame的grid权重
        filter_frame.grid_columnconfigure(1, weight=1)  # 文件类型下拉框列扩展
        filter_frame.grid_columnconfigure(3, weight=1)  # 文件大小下拉框列扩展
        
        # 文件类型过滤
        ttk.Label(filter_frame, text="文件类型:").grid(row=0, column=0, padx=(0, 5), sticky="w")
        
        self.file_type_var = tk.StringVar(value="所有文件")
        file_type_combo = ttk.Combobox(filter_frame, textvariable=self.file_type_var, width=15)
        file_type_combo["values"] = ("所有文件", "视频文件", "图片文件", "音频文件", "文档文件", "压缩文件", "其他文件")
        file_type_combo.grid(row=0, column=1, sticky="ew")
        file_type_combo.bind("<<ComboboxSelected>>", lambda e: self.filter_tree())
        
        # 文件大小过滤
        ttk.Label(filter_frame, text="文件大小:").grid(row=0, column=2, padx=(10, 5), sticky="w")
        
        self.file_size_var = tk.StringVar(value="所有大小")
        file_size_combo = ttk.Combobox(filter_frame, textvariable=self.file_size_var, width=15)
        file_size_combo["values"] = ("所有大小", "< 1MB", "1MB - 10MB", "10MB - 100MB", "100MB - 1GB", "> 1GB")
        file_size_combo.grid(row=0, column=3, sticky="ew")
        file_size_combo.bind("<<ComboboxSelected>>", lambda e: self.filter_tree())
        
        # 文件信息显示框架 - 使用ttk.Frame并设置padding，背景色浅蓝色
        info_frame = ttk.Frame(self.frame, padding=5, style="Info.TFrame")
        info_frame.grid(row=4, column=0, sticky="ew", padx=5, pady=(0, 5))
        
        # 配置info_frame的grid权重
        info_frame.grid_columnconfigure(1, weight=2)  # 文件名输入框列扩展更多
        info_frame.grid_columnconfigure(3, weight=1)  # 文件后缀输入框列扩展
        
        # 文件名显示
        ttk.Label(info_frame, text="文件名:").grid(row=0, column=0, padx=(0, 5), sticky="w")
        
        self.file_name_var = tk.StringVar(value="")
        self.file_name_entry = ttk.Entry(info_frame, textvariable=self.file_name_var, state="readonly")
        self.file_name_entry.grid(row=0, column=1, padx=(0, 10), sticky="ew")
        
        # 文件后缀显示
        ttk.Label(info_frame, text="文件后缀:").grid(row=0, column=2, padx=(0, 5), sticky="w")
        
        self.file_extension_var = tk.StringVar(value="")
        self.file_extension_entry = ttk.Entry(info_frame, textvariable=self.file_extension_var, state="readonly", width=15)
        self.file_extension_entry.grid(row=0, column=3, sticky="ew")
    
    def create_file_tree(self) -> None:
        """
        创建文件树
        
        遵循项目规则中的布局管理器配置和组件尺寸规范
        """
        # 创建树形视图框架 - 使用ttk.Frame并设置padding
        tree_frame = ttk.Frame(self.frame, padding=5)
        tree_frame.grid(row=5, column=0, sticky="nsew", padx=5, pady=5)
        
        # 配置tree_frame的grid权重
        tree_frame.grid_columnconfigure(0, weight=1)  # 树形视图列扩展
        tree_frame.grid_rowconfigure(0, weight=1)    # 树形视图行扩展
        
        # 配置self.frame的grid权重，使树形视图区域可以扩展
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_rowconfigure(5, weight=1)    # 树形视图行扩展
        
        # 创建树形视图（显示树形结构和列标题）
        self.tree = ttk.Treeview(tree_frame, columns=("name", "size", "type", "date", "path", "whitelist_type", "file_id"), show="tree headings")
        self.tree.grid(row=0, column=0, sticky="nsew")
        
        # 设置列宽和列标题
        self.tree.column("name", width=200, anchor="w")
        self.tree.column("size", width=100, anchor="e")
        self.tree.column("type", width=100, anchor="w")
        self.tree.column("date", width=150, anchor="w")
        self.tree.column("path", width=300, anchor="w")
        self.tree.column("whitelist_type", width=80, anchor="w")
        self.tree.column("file_id", width=0, anchor="w")  # 隐藏file_id列，但保留用于内部标识
        
        # 设置列标题
        self.tree.heading("name", text="文件名", command=lambda: self.sort_tree_column("name"))
        self.tree.heading("size", text="大小", command=lambda: self.sort_tree_column("size"))
        self.tree.heading("type", text="类型", command=lambda: self.sort_tree_column("type"))
        self.tree.heading("date", text="修改日期", command=lambda: self.sort_tree_column("date"))
        self.tree.heading("path", text="路径", command=lambda: self.sort_tree_column("path"))
        self.tree.heading("whitelist_type", text="白名单类型", command=lambda: self.sort_tree_column("whitelist_type"))
        self.tree.heading("file_id", text="文件ID", command=lambda: self.sort_tree_column("file_id"))
        
        # 创建滚动条
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        vsb.grid(row=0, column=1, sticky="ns")
        
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        hsb.grid(row=1, column=0, sticky="ew")
        
        self.tree.configure(yscrollcommand=self.on_vsb_changed, xscrollcommand=self.on_hsb_changed)
        
        # 配置滚动条样式
        style = self.main_window.style
        style.configure("Custom.Vertical.TScrollbar", 
                       gripcount=0,  # 去除滑块上的横线
                       background="#c1c1c1",  # 滚动条背景色
                       darkcolor="#6b6b6b",  # 滑块颜色
                       lightcolor="#6b6b6b",  # 滑块边框颜色
                       troughcolor="#e1e1e1",  # 滑槽颜色
                       arrowsize=14,  # 箭头大小
                       width=12)  # 滚动条宽度
        
        style.configure("Custom.Horizontal.TScrollbar",
                       gripcount=0,
                       background="#c1c1c1",
                       darkcolor="#6b6b6b",
                       lightcolor="#6b6b6b",
                       troughcolor="#e1e1e1",
                       arrowsize=14,
                       width=12)
        
        # 应用自定义样式
        vsb.configure(style="Custom.Vertical.TScrollbar")
        hsb.configure(style="Custom.Horizontal.TScrollbar")
        
        # 存储滚动条引用
        self.vsb = vsb
        self.hsb = hsb
        
        # 设置树形视图样式
        self.main_window.style.configure("Treeview", rowheight=25)
        self.main_window.style.configure("Treeview.Heading", font=("Helvetica", 10, "bold"))
        
        # 根据字体大小调整行高
        try:
            if hasattr(self.main_window, 'ui_factory') and self.main_window.ui_factory:
                font_size = self.main_window.ui_factory.get_current_font_size()
                row_height = max(25, font_size + 15)
                self.main_window.style.configure("Treeview", rowheight=row_height)
                self.main_window.style.configure("Treeview.Heading", font=("Helvetica", font_size, "bold"))
        except Exception as e:
            # 如果获取字体大小失败，使用默认值
            pass
        
        # 添加标签
        self.tree.tag_configure("folder", foreground="#0066cc", font=("Helvetica", 10, "bold"))
        self.tree.tag_configure("video", foreground="blue")
        self.tree.tag_configure("image", foreground="green")
        self.tree.tag_configure("audio", foreground="purple")
        self.tree.tag_configure("document", foreground="brown")
        self.tree.tag_configure("archive", foreground="orange")
        self.tree.tag_configure("junk", foreground="red")
        self.tree.tag_configure("whitelist", foreground="darkgreen")
        self.tree.tag_configure("duplicate", background="#ffe0e0")
        
        # 绑定懒加载展开事件
        self.tree.bind('<<TreeviewOpen>>', self._on_folder_expand_lazy)
    
    def create_context_menu(self) -> None:
        """
        创建上下文菜单
        
        创建文件树的右键上下文菜单，包含打开文件、打开所在文件夹、复制文件路径、
        删除文件、重命名文件、添加到白名单和标记为垃圾文件等操作选项。
        """
        try:
            # 使用self.tree作为父窗口，而不是self.frame
            self.context_menu = tk.Menu(self.tree, tearoff=0)
            # 添加菜单项并绑定相应的命令
            self.context_menu.add_command(label="打开文件", command=self.open_file)
            self.context_menu.add_command(label="打开所在文件夹", command=self.open_containing_folder)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="复制文件路径", command=self.copy_file_path)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="删除文件", command=self.delete_file)
            self.context_menu.add_command(label="重命名文件", command=self.rename_file)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="添加到白名单", command=self.add_to_whitelist)
            
            # 设置文件类型子菜单
            file_type_menu = tk.Menu(self.context_menu, tearoff=0)
            for t in ["JP", "CH", "ENG"]:
                file_type_menu.add_command(label=f"设置为{t}类型", command=lambda t=t: self.set_file_type(t))
            self.context_menu.add_cascade(label="设置文件类型", menu=file_type_menu)
            
            # 新增批量设置白名单类型子菜单
            whitelist_type_menu = tk.Menu(self.context_menu, tearoff=0)
            for t in ["CH", "EN", "其他"]:
                whitelist_type_menu.add_command(label=f"批量设置为{t}", command=lambda t=t: self.batch_set_whitelist_type(t))
            self.context_menu.add_cascade(label="批量设置白名单类型", menu=whitelist_type_menu)
            self.context_menu.add_command(label="标记为垃圾文件", command=self.mark_as_junk)
            self.context_menu.add_separator()
            self.context_menu.add_command(label="刷新文件树", command=self.refresh_file_tree)
        except Exception as e:
            logging.error(f"创建上下文菜单时出错: {str(e)}")
            messagebox.showerror("错误", f"创建上下文菜单时出错: {str(e)}")
    
    def bind_events(self):
        """绑定事件"""
        # 绑定搜索框事件
        self.search_var.trace('w', lambda *args: self.filter_tree())
        
        # 绑定树形视图事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_single_click)
        self.tree.bind('<Double-1>', self.on_tree_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)
        
        # ----------- 新增：绑定滚动和展开事件，触发懒加载刷新 -----------
        self.tree.bind('<MouseWheel>', self._on_tree_scroll)  # Windows
        self.tree.bind('<Button-4>', self._on_tree_scroll)    # Linux向上滚动
        self.tree.bind('<Button-5>', self._on_tree_scroll)    # Linux向下滚动
        self.tree.bind('<<TreeviewOpen>>', self._on_folder_expand_lazy)  # 懒加载
        self.tree.bind('<<TreeviewClose>>', self._on_tree_expand) # 关闭节点
        
        # 绑定列标题点击事件
        for col in ['name', 'size', 'date', 'type']:
            self.tree.heading(col, command=lambda c=col: self.sort_tree_column(c, True))
    
    def on_mousewheel(self, event, delta=None) -> str:
        """
        处理垂直滚动的鼠标滚轮事件
        
        参数:
            event: 鼠标滚轮事件对象
            delta: 可选的滚动量，用于Linux平台的Button-4和Button-5事件
        
        返回:
            str: "break"字符串，用于阻止事件继续传播
        """
        try:
            # 如果提供了delta参数（Linux平台），直接使用
            if delta is not None:
                scroll_delta = delta
            else:
                # 在Windows上，event.delta的值是120的倍数
                # 将其转换为更合适的滚动量
                scroll_delta = -1 if event.delta < 0 else 1
            
            # 执行垂直滚动
            self.tree.yview_scroll(-scroll_delta, "units")
        except Exception as e:
            logging.error(f"处理鼠标滚轮事件时出错: {str(e)}")
        
        return "break"  # 阻止事件继续传播
    
    def on_shift_mousewheel(self, event, delta=None) -> str:
        """
        处理水平滚动的鼠标滚轮事件（按住Shift键）
        
        参数:
            event: 鼠标滚轮事件对象
            delta: 可选的滚动量，用于Linux平台的Shift-Button-4和Shift-Button-5事件
        
        返回:
            str: "break"字符串，用于阻止事件继续传播
        """
        try:
            # 如果提供了delta参数（Linux平台），直接使用
            if delta is not None:
                scroll_delta = delta
            else:
                # 在Windows上，event.delta的值是120的倍数
                # 将其转换为更合适的滚动量
                scroll_delta = -1 if event.delta < 0 else 1
            
            # 执行水平滚动
            self.tree.xview_scroll(-scroll_delta, "units")
        except Exception as e:
            logging.error(f"处理Shift+鼠标滚轮事件时出错: {str(e)}")
        
        return "break"  # 阻止事件继续传播
    
    def on_vsb_changed(self, *args) -> None:
        """
        垂直滚动条值改变的回调函数
        
        参数:
            *args: 滚动条位置参数
        """
        try:
            # 更新滚动条位置
            self.vsb.set(*args)
            # 如果内容发生变化，重新计算滚动区域
            self.update_scrollregion()
        except Exception as e:
            logging.error(f"更新垂直滚动条时出错: {str(e)}")
    
    def on_hsb_changed(self, *args) -> None:
        """
        水平滚动条值改变的回调函数
        
        参数:
            *args: 滚动条位置参数
        """
        try:
            # 更新滚动条位置
            self.hsb.set(*args)
            # 如果内容发生变化，重新计算滚动区域
            self.update_scrollregion()
        except Exception as e:
            logging.error(f"更新水平滚动条时出错: {str(e)}")
    
    def update_scrollregion(self) -> None:
        """
        更新滚动区域
        
        根据树形视图的实际大小更新滚动区域，确保滚动条能够正确显示和操作。
        """
        try:
            # 获取树形视图的实际大小
            bbox = self.tree.bbox("")
            if bbox:
                # 设置滚动条的滚动范围
                self.tree['scrollregion'] = bbox
        except Exception as e:
            logging.error(f"更新滚动区域时出错: {str(e)}")
    
    def _ensure_attributes_initialized(self):
        """确保所有必要属性已初始化"""
        if not hasattr(self, 'use_memory_mode'):
            self.use_memory_mode = True
            self.logger.info("[文件树] 初始化 use_memory_mode 属性")

        if not hasattr(self, 'memory_manager'):
            self.memory_manager = None
            self.logger.info("[文件树] 初始化 memory_manager 属性")

        if not hasattr(self, 'virtualized_renderer'):
            self.virtualized_renderer = None
            self.logger.info("[文件树] 初始化 virtualized_renderer 属性")

    def update_file_tree(self, data):
        """更新文件树（支持内存模式和传统模式，安全版本）"""
        try:
            # 确保属性已初始化
            self._ensure_attributes_initialized()

            if not data:
                self.logger.warning("更新文件树时收到空数据")
                return

            # 安全检查内存模式
            if (hasattr(self, 'use_memory_mode') and self.use_memory_mode and
                hasattr(self, 'memory_manager') and self.memory_manager):
                self._update_file_tree_memory_mode(data)
            else:
                self._update_file_tree_traditional_mode(data)

        except Exception as e:
            self.logger.error(f"更新文件树失败: {e}")

    def _update_file_tree_memory_mode(self, data):
        """内存模式更新文件树"""
        try:
            self.logger.info("[文件树] 使用内存模式更新文件树")

            # 如果内存未加载，启动异步加载
            if not self.memory_manager.is_loaded and not self.memory_loading:
                self._start_memory_loading()
                return

            # 如果正在加载，显示加载状态
            if self.memory_loading:
                self._show_loading_status()
                return

            # 内存已加载，使用虚拟化渲染
            if not self.virtualized_renderer:
                self._init_virtualized_renderer()

            if self.virtualized_renderer:
                self.virtualized_renderer.render_from_memory()
            else:
                # 如果没有虚拟化渲染器，使用内存模式的populate_tree
                self.populate_tree_memory_mode(data.get("files", {}))

            # 更新统计信息
            stats = self.memory_manager.get_statistics()
            self._update_stats_from_memory(stats)

        except Exception as e:
            self.logger.error(f"内存模式更新文件树失败: {e}")
            # 降级到传统模式
            self.use_memory_mode = False
            self._update_file_tree_traditional_mode(data)

    def _update_file_tree_traditional_mode(self, data):
        """传统模式更新文件树"""
        try:
            self.logger.info("[文件树] 使用传统模式更新文件树")
            files = data.get("files", {})
            directory = data.get("directory", "")
            file_list = list(files.values())
            # 新增：过滤掉file_path/path/filename为空的条目
            file_list = [f for f in file_list if (f.get('file_path') or f.get('path') or f.get('filepath'))]
            
            # 基于文件路径的防重复机制
            if not hasattr(self, '_current_file_paths'):
                self._current_file_paths = set()
            if not hasattr(self, '_last_update_hash'):
                self._last_update_hash = None
            if not hasattr(self, '_last_update_time'):
                self._last_update_time = 0
            
            # 提取当前文件路径集合
            current_paths = set()
            for file_info in file_list:
                file_path = file_info.get('file_path') or file_info.get('path') or file_info.get('filepath', '')
                if file_path:
                    current_paths.add(_normalize_path(file_path))
            
            # 计算当前文件集合的哈希值
            current_hash = hash(frozenset(current_paths))
            current_time = time.time()
            
            # 检查文件路径是否发生变化（同时考虑时间窗口）
            if (current_hash == self._last_update_hash and 
                self._last_update_hash is not None and 
                current_time - self._last_update_time < 5.0):  # 5秒内跳过重复更新
                self.logger.info(f"文件路径集合未变化({len(current_paths)}个文件)，跳过重复更新")
                return
            
            # 检查文件路径集合是否完全相同
            if current_paths == self._current_file_paths and len(self._current_file_paths) > 0:
                self.logger.info(f"文件路径集合完全相同({len(current_paths)}个文件)，跳过重复更新")
                return
            
            self.logger.info(f"[文件树] update_file_tree收到{len(files)}个文件，准备刷新...")
            
            # 添加路径格式调试日志
            if file_list:
                sample_paths = [f.get('file_path') or f.get('path') or f.get('filepath', '') for f in file_list[:3]]
                self.logger.debug(f'[文件树] 原始路径样本: {sample_paths}')
            
            def after_whitelist_refresh():
                # 输出前5个文件的path样本
                sample_paths = [f.get('file_path') or f.get('path') or f.get('filepath', '') for f in list(files.values())[:5]]
                self.logger.debug(f'[文件树] 白名单刷新后路径样本: {sample_paths}')
                self.logger.info(f"[文件树] after_whitelist_refresh被调用，文件数: {len(files)}")
                self.files = files  # 关键：赋值！
                self.tree.delete(*self.tree.get_children())
                if files:
                    self.logger.info("[文件树] 调用populate_tree...")
                    self.populate_tree(files)
                
                # 更新文件路径集合、哈希值和时间戳
                self._current_file_paths = current_paths
                self._last_update_hash = current_hash
                self._last_update_time = current_time
                
                self.logger.info(f"文件树更新完成，共 {len(files)} 个文件")
                
                # 直接计算并更新文件统计信息
                self.calculate_file_stats()
            # ----------- 优化：智能白名单检查策略 -----------
            # 对于大量文件，跳过白名单检查以提高性能
            if len(file_list) > 1000:
                self.logger.info(f"文件量很大({len(file_list)}个)，跳过白名单检查以提高性能")
                after_whitelist_refresh()
            elif self.auto_whitelist_check and len(file_list) <= 1000:
                # 中等数量文件才进行白名单检查
                if len(file_list) > 200 and self.concurrent_refresh_enabled:
                    self.logger.info(f"文件量较大({len(file_list)}个)，使用并发刷新")
                    self._refresh_whitelist_status_concurrent(file_list, batch_size=100, on_complete=after_whitelist_refresh)
                else:
                    self.logger.info(f"文件量较小({len(file_list)}个)，使用普通批量刷新")
                    self._refresh_whitelist_status_in_batches(file_list, batch_size=100, on_complete=after_whitelist_refresh)
            else:
                # 跳过自动白名单检查，直接加载文件树（推荐）
                self.logger.info(f"跳过自动白名单检查，直接加载 {len(file_list)} 个文件到文件树")
                after_whitelist_refresh()
        except Exception as e:
            self.logger.error(f"更新文件树时出错: {str(e)}")
            messagebox.showerror("错误", f"更新文件树时出错: {str(e)}")

    def _refresh_whitelist_status_concurrent(self, file_list, batch_size=100, on_complete=None):
        if not self.concurrent_refresh_enabled or len(file_list) < 50:
            return self._refresh_whitelist_status_in_batches(file_list, batch_size, on_complete)
        try:
            self.logger.info(f"开始并发刷新 {len(file_list)} 个文件的白名单状态")
            self._clear_expired_cache()
            batches = [file_list[i:i + batch_size] for i in range(0, len(file_list), batch_size)]
            total_batches = len(batches)
            completed_batches = 0
            def process_batch(batch_files):
                try:
                    file_scanner = None
                    if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                        file_scanner = self.main_window.file_scanner
                    if not file_scanner:
                        return
                    from src.data.models import FileInfo
                    for f in batch_files:
                        try:
                            # 使用统一的路径字段获取
                            file_path = f.get('file_path') or f.get('path') or f.get('filepath')
                            if not file_path:
                                self.logger.error(f'[文件树] 跳过path为空的文件: {f}')
                                continue
                            
                            # 标准化路径
                            file_path = _normalize_path(file_path)
                            
                            cached_result = self._get_cached_whitelist_status(file_path)
                            if cached_result is not None:
                                f['is_whitelist'] = cached_result
                                continue
                            file_obj = FileInfo.from_dict(f)
                            # 创建兼容的FileInfo对象
                            compatible_file_obj = self._create_compatible_file_info(file_obj)
                            is_whitelist = file_scanner.is_whitelist_file(compatible_file_obj)
                            f['is_whitelist'] = is_whitelist
                            self._set_cached_whitelist_status(file_path, is_whitelist)
                        except Exception as e:
                            self.logger.error(f"处理文件 {f.get('name', 'unknown')} 失败: {e}")
                except Exception as e:
                    self.logger.error(f"处理批次失败: {e}")
            def schedule_next_batch():
                nonlocal completed_batches
                with self.refresh_lock:
                    if completed_batches < total_batches:
                        batch = batches[completed_batches]
                        thread = threading.Thread(target=process_batch, args=(batch,))
                        thread.daemon = True
                        thread.start()
                        completed_batches += 1
                        if completed_batches >= total_batches:
                            if on_complete:
                                self.logger.info("[文件树] 并发白名单刷新完成，调用on_complete...")
                                self.frame.after(10, on_complete)
                        else:
                            self.frame.after(50, schedule_next_batch)
                    else:
                        if on_complete:
                            self.logger.info("[文件树] 并发白名单刷新提前完成，调用on_complete...")
                            self.frame.after(10, on_complete)
            schedule_next_batch()
        except Exception as e:
            self.logger.error(f"并发刷新白名单状态失败: {e}")
            if on_complete:
                self.logger.info("[文件树] 并发刷新异常，调用on_complete...")
                on_complete()

    def _refresh_whitelist_status_in_batches(self, file_list, batch_size=100, on_complete=None):
        file_scanner = None
        if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
            file_scanner = self.main_window.file_scanner
        if not file_scanner:
            try:
                from src.core.file_scanner import FileScanner
                from src.core.dependency_injection import resolve
                file_scanner = resolve(FileScanner)
            except Exception as e:
                self.logger.error(f"无法获取file_scanner实例: {e}")
                if on_complete:
                    self.logger.info("[文件树] 批量刷新异常，调用on_complete...")
                    on_complete()
                return
        try:
            self.logger.info(f"开始批量刷新 {len(file_list)} 个文件的白名单状态")
            self._clear_expired_cache()
            total = len(file_list)
            def process_batch(start):
                end = min(start + batch_size, total)
                for i in range(start, end):
                    f = file_list[i]
                    # 使用统一的路径字段获取
                    file_path = f.get('file_path') or f.get('path') or f.get('filepath')
                    if not file_path:
                        self.logger.error(f'[文件树] 跳过path为空的文件: {f}')
                        continue
                    
                    # 标准化路径
                    file_path = _normalize_path(file_path)
                    
                    cached_result = self._get_cached_whitelist_status(file_path)
                    if cached_result is not None:
                        f['is_whitelist'] = cached_result
                        continue
                    from src.data.models import FileInfo
                    file_obj = FileInfo.from_dict(f)
                    # 创建兼容的FileInfo对象
                    compatible_file_obj = self._create_compatible_file_info(file_obj)
                    is_whitelist = file_scanner.is_whitelist_file(compatible_file_obj)
                    f['is_whitelist'] = is_whitelist
                    self._set_cached_whitelist_status(file_path, is_whitelist)
                percent = int((end / total) * 100) if total else 100
                if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.update_progress(percent, f"白名单刷新中...({end}/{total})")
                if end < total:
                    self.frame.after(1, lambda: process_batch(end))
                else:
                    if on_complete:
                        self.logger.info("[文件树] 批量白名单刷新完成，调用on_complete...")
                        self.frame.after(10, on_complete)
            process_batch(0)
        except Exception as e:
            self.logger.error(f"批量刷新白名单状态失败: {e}")
            if on_complete:
                self.logger.info("[文件树] 批量刷新异常，调用on_complete...")
                on_complete()

    def _get_cached_whitelist_status(self, file_path: str) -> Optional[bool]:
        """
        从缓存获取白名单状态
        返回None表示缓存未命中或已过期
        """
        try:
            if file_path in self.whitelist_cache:
                is_whitelist, timestamp = self.whitelist_cache[file_path]
                current_time = time.time()
                
                # 检查缓存是否过期
                if current_time - timestamp < self.cache_ttl:
                    return is_whitelist
                else:
                    # 缓存过期，删除
                    del self.whitelist_cache[file_path]
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取缓存白名单状态失败: {e}")
            return None
    
    def _set_cached_whitelist_status(self, file_path: str, is_whitelist: bool) -> None:
        """设置白名单状态缓存"""
        try:
            self.whitelist_cache[file_path] = (is_whitelist, time.time())
        except Exception as e:
            self.logger.error(f"设置缓存白名单状态失败: {e}")
    
    def _clear_expired_cache(self) -> None:
        """清理过期的缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for file_path, (_, timestamp) in self.whitelist_cache.items():
                if current_time - timestamp >= self.cache_ttl:
                    expired_keys.append(file_path)
            
            for key in expired_keys:
                del self.whitelist_cache[key]
                
            if expired_keys:
                self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存")
                
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
    
    def clear_tree(self) -> None:
        """
        清空树形视图
        
        删除树形视图中的所有项目，为重新填充做准备。
        """
        try:
            for item in self.tree.get_children():
                self.tree.delete(item)
            self.logger.debug("已清空文件树视图")
        except Exception as e:
            self.logger.error(f"清空树形视图时出错: {str(e)}")
    
    def _build_tree_dict(self, file_list):
        tree_dict = {}
        seen_paths = set()
        for file in file_list:
            file_path = file.get('file_path') or file.get('path') or file.get('filepath')
            if not file_path:
                self.logger.error(f'[文件树] 跳过file_path为空的文件: {file}')
                continue
            original_path = file_path
            file_path = _normalize_path(file_path)
            if file_path in seen_paths:
                self.logger.warning(f'[文件树] 检测到重复文件路径: {file_path}, 原始: {original_path}')
            seen_paths.add(file_path)
            if original_path != file_path:
                self.logger.debug(f'[文件树] 路径标准化: {original_path} -> {file_path}')
            file['file_path'] = file_path
            tree_dict[file_path] = file
        self.logger.debug(f'[文件树] _build_tree_dict生成的所有路径: {list(tree_dict.keys())}')
        return tree_dict

    def populate_tree(self, files, batch_size=50):
        """构建文件树结构（优化版本）"""
        self.logger.info("[文件树] 开始构建文件树结构...")
        if isinstance(files, dict):
            file_list = list(files.values())
        else:
            file_list = files

        # 过滤掉file_path/path/filename为空的条目
        file_list = [f for f in file_list if (f.get('file_path') or f.get('path') or f.get('filepath'))]

        # 检查文件数量，如果太多则使用优化策略
        if len(file_list) > 1000:
            self.logger.info(f"[文件树] 文件数量较多({len(file_list)}个)，使用优化加载策略")
            self._populate_tree_optimized(file_list, batch_size=25)
        else:
            self.logger.info(f"[文件树] 文件数量适中({len(file_list)}个)，使用标准加载策略")
            # 按depth分组
            depth_map = {}
            for f in file_list:
                depth = f.get('depth', 1)
                depth_map.setdefault(depth, []).append(f)
            all_depths = sorted(depth_map.keys())
            self.logger.info(f"[文件树] 文件分布层级: {all_depths}")
            self.tree.delete(*self.tree.get_children())
            self.folder_placeholder_items.clear()
            self.lazy_loaded_folders = set()
            self.expanding_folders = set()
            self.folder_children_cache = {}

            # 重置文件索引
            self.file_index_by_parent.clear()
            self.file_index_built = False

            # 启动优化的批量分层加载
            self._batched_strict_layer_load_optimized(depth_map, all_depths, batch_size, 0, 0, {}, set(), set())

    def populate_tree_memory_mode(self, files, batch_size=100):
        """构建文件树结构（内存模式，支持中断和进度跟踪）"""
        try:
            # 检查中断
            if hasattr(self.main_window, 'interrupt_event') and self.main_window.interrupt_event.is_set():
                self.logger.info("[文件树] 文件树构建被中断")
                return

            self.logger.info("[文件树] 开始构建文件树结构（内存模式）...")

            # 初始化构建状态
            self._init_tree_building_state(files, batch_size)

            # 启动异步构建
            self._start_async_tree_building()

        except Exception as e:
            self.logger.error(f"[文件树] 启动文件树构建失败: {e}")
            self._reset_tree_building_state()

    def _init_tree_building_state(self, files, batch_size):
        """初始化文件树构建状态"""
        import time

        # 处理文件数据
        if isinstance(files, dict):
            file_list = list(files.values())
        else:
            file_list = files

        # 过滤掉file_path/path/filename为空的条目
        file_list = [f for f in file_list if (f.get('file_path') or f.get('path') or f.get('filepath'))]

        # 按depth分组
        depth_map = {}
        for f in file_list:
            depth = f.get('depth', 1)
            depth_map.setdefault(depth, []).append(f)
        all_depths = sorted(depth_map.keys())

        # 计算总文件数和层级信息
        total_files = len(file_list)
        total_layers = len(all_depths)

        # 存储构建状态
        self.tree_building_state = {
            'depth_map': depth_map,
            'all_depths': all_depths,
            'batch_size': batch_size,
            'total_files': total_files,
            'total_layers': total_layers,
            'processed_files': 0,
            'current_layer': 0,
            'current_layer_index': 0,
            'folder_nodes': {},
            'created_roots': set(),
            'inserted_files': set(),
            'start_time': time.time(),
            'is_building': True
        }

        # 清理UI状态
        self.tree.delete(*self.tree.get_children())
        self.folder_placeholder_items.clear()
        self.lazy_loaded_folders = set()
        self.expanding_folders = set()
        self.folder_children_cache = {}

        self.logger.info(f"[文件树] 构建初始化完成 - 总文件数: {total_files}, 总层级数: {total_layers}, 层级分布: {all_depths}, 批次大小: {batch_size}")

    def _start_async_tree_building(self):
        """启动异步文件树构建"""
        # 更新状态栏
        if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
            self.main_window.status_bar.update_progress(0, "开始构建文件树...")
            self.main_window.status_bar.enable_stop_button()

        # 启动第一批处理
        self._process_next_batch()

    def _process_next_batch(self):
        """处理下一批文件（支持中断和进度跟踪）"""
        try:
            # 检查中断
            if hasattr(self.main_window, 'interrupt_event') and self.main_window.interrupt_event.is_set():
                self.logger.info("[文件树] 文件树构建在批处理中被中断")
                self._reset_tree_building_state()
                return

            # 检查构建状态
            if not hasattr(self, 'tree_building_state') or not self.tree_building_state.get('is_building'):
                self.logger.warning("[文件树] 构建状态异常，停止处理")
                return

            state = self.tree_building_state

            # 检查是否完成
            if state['current_layer'] >= len(state['all_depths']):
                self._complete_tree_building()
                return

            # 获取当前层级信息
            current_depth = state['all_depths'][state['current_layer']]
            layer_files = state['depth_map'][current_depth]

            # 计算当前批次范围
            start_index = state['current_layer_index']
            end_index = min(start_index + state['batch_size'], len(layer_files))

            # 处理当前批次
            processed_count = self._process_batch_files(
                layer_files[start_index:end_index],
                current_depth,
                start_index
            )

            # 更新状态
            state['processed_files'] += processed_count
            state['current_layer_index'] = end_index

            # 更新进度和日志
            self._update_building_progress()

            # 判断下一步操作
            if end_index >= len(layer_files):
                # 当前层级完成，进入下一层
                state['current_layer'] += 1
                state['current_layer_index'] = 0
                self.logger.info(f"[文件树] 第{state['current_layer']}层级(深度{current_depth})构建完成，共处理{len(layer_files)}个文件")

            # 调度下一批处理
            self.frame.after(1, self._process_next_batch)

        except Exception as e:
            self.logger.error(f"[文件树] 批处理过程中出错: {e}")
            self._reset_tree_building_state()

    def _process_batch_files(self, batch_files, current_depth, start_index):
        """处理一批文件"""
        processed_count = 0
        state = self.tree_building_state

        for i, file_info in enumerate(batch_files):
            # 检查中断
            if hasattr(self.main_window, 'interrupt_event') and self.main_window.interrupt_event.is_set():
                self.logger.info(f"[文件树] 文件处理在第{start_index + i}个文件时被中断")
                break

            try:
                # 获取文件路径
                file_path = file_info.get('file_path') or file_info.get('path') or file_info.get('filepath')
                if not file_path:
                    continue

                from src.utils.format_utils import _normalize_path
                normalized_path = _normalize_path(file_path)

                if not normalized_path or not os.path.basename(normalized_path):
                    self.logger.warning(f'[文件树] 跳过无效路径: {file_path}')
                    continue

                # 检查重复
                if normalized_path in state['inserted_files']:
                    self.logger.debug(f'[文件树] 跳过重复文件: {normalized_path}')
                    continue

                # 处理文件插入
                if self._insert_file_to_tree(normalized_path, file_info, state):
                    state['inserted_files'].add(normalized_path)
                    processed_count += 1

            except Exception as e:
                self.logger.error(f"[文件树] 处理文件时出错: {e}")
                continue

        return processed_count

    def _insert_file_to_tree(self, normalized_path, file_info, state):
        """将文件插入到树中"""
        try:
            import os

            # 获取根目录
            root_dir = self._get_root_directory(normalized_path)

            # 创建根节点（如果不存在）
            if root_dir not in state['created_roots']:
                root_folder_name = self._get_display_name_for_root(root_dir)
                root_node = self.tree.insert(
                    "", "end",
                    text=root_folder_name,
                    values=(root_folder_name, "", "根目录", "", root_dir),
                    tags=("folder", "root"),
                    open=False  # 修复：不自动展开根文件夹，避免触发大量懒加载
                )
                state['folder_nodes'][root_dir] = root_node
                state['created_roots'].add(root_dir)
                self.logger.debug(f"[文件树] 创建根节点: {root_folder_name}")

            # 计算相对路径
            try:
                # 确保路径格式一致
                normalized_root = root_dir.replace('\\', '/')
                normalized_file = normalized_path.replace('\\', '/')
                relative_path = os.path.relpath(normalized_file, normalized_root)
            except ValueError:
                relative_path = normalized_path

            relative_path = relative_path.replace('\\', '/')
            path_parts = relative_path.split('/')

            # 如果是根目录下的直接文件
            if len(path_parts) == 1:
                self.add_file_to_tree(state['folder_nodes'][root_dir], normalized_path, file_info)
                return True

            # 创建中间文件夹节点
            current_parent = state['folder_nodes'][root_dir]
            current_path = root_dir

            for folder_name in path_parts[:-1]:
                if not folder_name:
                    continue

                full_folder_path = os.path.join(current_path, folder_name).replace('\\', '/')

                if full_folder_path not in state['folder_nodes']:
                    display_name = self._get_unique_display_name(full_folder_path, state['folder_nodes'], root_dir)

                    if self._has_duplicate_folder_name(folder_name, state['folder_nodes'], current_path):
                        self._update_existing_display_names(folder_name, state['folder_nodes'])
                        display_name = self._get_unique_display_name(full_folder_path, state['folder_nodes'], root_dir)

                    folder_node = self.tree.insert(
                        current_parent, "end",
                        text=display_name,
                        values=(display_name, "", "文件夹", "", full_folder_path),
                        tags=("folder", "directory"),
                        open=False
                    )
                    state['folder_nodes'][full_folder_path] = folder_node
                    self.logger.debug(f"[文件树] 创建文件夹节点: {display_name}")

                current_parent = state['folder_nodes'][full_folder_path]
                current_path = full_folder_path

            # 添加文件到最终的父文件夹
            self.add_file_to_tree(current_parent, normalized_path, file_info)
            return True

        except Exception as e:
            self.logger.error(f"[文件树] 插入文件失败 {normalized_path}: {e}")
            return False

    def _update_building_progress(self):
        """更新构建进度"""
        try:
            import time

            state = self.tree_building_state

            # 计算进度百分比
            progress_percent = int((state['processed_files'] / state['total_files']) * 100) if state['total_files'] > 0 else 0

            # 计算耗时
            elapsed_time = time.time() - state['start_time']

            # 估算剩余时间
            if state['processed_files'] > 0:
                avg_time_per_file = elapsed_time / state['processed_files']
                remaining_files = state['total_files'] - state['processed_files']
                estimated_remaining = avg_time_per_file * remaining_files
            else:
                estimated_remaining = 0

            # 构建状态消息
            current_layer = state['current_layer'] + 1
            total_layers = state['total_layers']
            current_depth = state['all_depths'][state['current_layer']] if state['current_layer'] < len(state['all_depths']) else 'N/A'

            status_msg = f"构建文件树 第{current_layer}/{total_layers}层(深度{current_depth}) {state['processed_files']}/{state['total_files']}文件 {progress_percent}%"

            # 更新状态栏
            if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
                self.main_window.status_bar.update_progress(progress_percent, status_msg)

            # 详细日志（每10%或每1000个文件记录一次）
            if (state['processed_files'] % 1000 == 0) or (progress_percent % 10 == 0 and progress_percent != state.get('last_logged_percent', -1)):
                self.logger.info(f"[文件树] 构建进度: {progress_percent}% ({state['processed_files']}/{state['total_files']}) "
                               f"当前层级: {current_layer}/{total_layers}(深度{current_depth}) "
                               f"耗时: {elapsed_time:.1f}s 预计剩余: {estimated_remaining:.1f}s")
                state['last_logged_percent'] = progress_percent

        except Exception as e:
            self.logger.error(f"[文件树] 更新进度失败: {e}")

    def _complete_tree_building(self):
        """完成文件树构建"""
        try:
            import time

            state = self.tree_building_state
            total_time = time.time() - state['start_time']

            # 记录完成日志
            for item in self.tree.get_children(""):
                self._log_folder_children(item, level=0)

            # 更新状态栏
            if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
                self.main_window.status_bar.update_progress(100, f"文件树构建完成，共{state['processed_files']}个文件")
                self.main_window.status_bar.disable_stop_button()

            self.logger.info(f"[文件树] 文件树构建完成！总文件数: {state['processed_files']}, "
                           f"总层级数: {state['total_layers']}, 总耗时: {total_time:.2f}秒, "
                           f"平均速度: {state['processed_files']/total_time:.1f}文件/秒")

            # 输出重复文件夹汇总报告
            self._report_duplicate_folder_summary()

            # 重置构建状态
            self._reset_tree_building_state()

            # 清理重复检测缓存
            self._clear_duplicate_cache()

        except Exception as e:
            self.logger.error(f"[文件树] 完成构建时出错: {e}")
            self._reset_tree_building_state()

    def _reset_tree_building_state(self):
        """重置文件树构建状态"""
        if hasattr(self, 'tree_building_state'):
            self.tree_building_state['is_building'] = False

        # 重置状态栏
        if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
            self.main_window.status_bar.disable_stop_button()

    def _build_file_index(self):
        """构建文件索引以优化懒加载性能"""
        if self.file_index_built:
            return

        self.logger.info("[文件树] 开始构建文件索引...")
        start_time = time.time()

        self.file_index_by_parent.clear()

        for file_path, file_info in self.files.items():
            try:
                normalized_path = _normalize_path(file_path)
                parent_path = os.path.dirname(normalized_path)

                if parent_path not in self.file_index_by_parent:
                    self.file_index_by_parent[parent_path] = []

                self.file_index_by_parent[parent_path].append((normalized_path, file_info))

            except Exception as e:
                self.logger.error(f"构建文件索引失败: {file_path}, 错误: {e}")
                continue

        self.file_index_built = True
        elapsed_time = time.time() - start_time
        self.logger.info(f"[文件树] 文件索引构建完成，耗时: {elapsed_time:.2f}秒，索引了 {len(self.file_index_by_parent)} 个目录")

    def _populate_tree_optimized(self, file_list, batch_size=25):
        """优化的文件树构建方法，适用于大量文件"""
        self.logger.info(f"[文件树] 开始优化构建，文件数: {len(file_list)}")

        # 清理UI状态
        self.tree.delete(*self.tree.get_children())
        self.folder_placeholder_items.clear()
        self.lazy_loaded_folders = set()
        self.expanding_folders = set()
        self.folder_children_cache = {}

        # 重置文件索引
        self.file_index_by_parent.clear()
        self.file_index_built = False

        # 初始化构建状态
        self.tree_build_state = {
            'files': file_list,
            'batch_size': batch_size,
            'current_index': 0,
            'total_files': len(file_list),
            'folder_nodes': {},
            'created_roots': set(),
            'inserted_files': set(),
            'start_time': time.time()
        }

        # 启动异步构建
        self._process_next_batch_optimized()

    def _process_next_batch_optimized(self):
        """处理下一批文件（优化版本）"""
        try:
            state = self.tree_build_state
            start_idx = state['current_index']
            end_idx = min(start_idx + state['batch_size'], state['total_files'])

            # 检查是否完成
            if start_idx >= state['total_files']:
                self._finish_tree_building()
                return

            # 处理当前批次
            batch_files = state['files'][start_idx:end_idx]
            processed_count = 0

            for file_info in batch_files:
                try:
                    # 检查中断
                    if hasattr(self.main_window, 'interrupt_event') and self.main_window.interrupt_event.is_set():
                        self.logger.info("[文件树] 构建被中断")
                        return

                    file_path = file_info.get('file_path') or file_info.get('path') or file_info.get('filepath')
                    if not file_path:
                        continue

                    normalized_path = _normalize_path(file_path)
                    if normalized_path in state['inserted_files']:
                        continue

                    # 跳过目录条目
                    if file_info.get('is_dir') is True:
                        continue

                    # 简化的文件插入逻辑
                    self._insert_file_optimized(normalized_path, file_info, state)
                    state['inserted_files'].add(normalized_path)
                    processed_count += 1

                except Exception as e:
                    self.logger.error(f"处理文件失败: {e}")
                    continue

            # 更新进度
            state['current_index'] = end_idx
            progress = (end_idx / state['total_files']) * 100

            if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
                self.main_window.status_bar.update_progress(
                    progress,
                    f"构建文件树: {end_idx}/{state['total_files']}"
                )

            self.logger.debug(f"[文件树] 处理批次 {start_idx}-{end_idx}, 插入 {processed_count} 个文件")

            # 调度下一批次，使用更长的间隔以保持UI响应
            self.frame.after(10, self._process_next_batch_optimized)

        except Exception as e:
            self.logger.error(f"[文件树] 批次处理失败: {e}")
            self._finish_tree_building()

    def _insert_file_optimized(self, file_path, file_info, state):
        """优化的文件插入方法"""
        try:
            # 获取根目录
            root_dir = self._get_root_directory(file_path)

            # 创建根节点（如果不存在）
            if root_dir not in state['created_roots']:
                root_folder_name = self._get_display_name_for_root(root_dir)
                root_node = self.tree.insert(
                    "", "end",
                    text=root_folder_name,
                    values=(root_folder_name, "", "根目录", "", root_dir),
                    tags=("folder", "root"),
                    open=False  # 修复：不自动展开根文件夹，避免触发大量懒加载
                )
                state['folder_nodes'][root_dir] = root_node
                state['created_roots'].add(root_dir)

            # 简化路径处理
            try:
                relative_path = os.path.relpath(file_path, root_dir)
            except ValueError:
                relative_path = file_path

            relative_path = relative_path.replace('\\', '/')
            path_parts = relative_path.split('/')

            # 如果是根目录下的文件，直接插入
            if len(path_parts) == 1:
                self.add_file_to_tree(state['folder_nodes'][root_dir], file_path, file_info)
                return

            # 创建必要的文件夹节点
            current_parent = state['folder_nodes'][root_dir]
            current_path = root_dir

            for folder_name in path_parts[:-1]:
                if not folder_name:
                    continue

                full_folder_path = os.path.join(current_path, folder_name).replace('\\', '/')

                if full_folder_path not in state['folder_nodes']:
                    display_name = os.path.basename(full_folder_path) or "根目录"
                    folder_node = self.tree.insert(
                        current_parent, "end",
                        text=display_name,
                        values=(display_name, "", "文件夹", "", full_folder_path),
                        tags=("folder", "directory"),
                        open=False
                    )
                    state['folder_nodes'][full_folder_path] = folder_node

                current_parent = state['folder_nodes'][full_folder_path]
                current_path = full_folder_path

            # 插入文件
            self.add_file_to_tree(current_parent, file_path, file_info)

        except Exception as e:
            self.logger.error(f"插入文件失败 {file_path}: {e}")

    def _finish_tree_building(self):
        """完成文件树构建"""
        try:
            if hasattr(self, 'tree_build_state'):
                elapsed_time = time.time() - self.tree_build_state['start_time']
                total_files = self.tree_build_state['total_files']
                self.logger.info(f"[文件树] 构建完成，共 {total_files} 个文件，耗时 {elapsed_time:.2f} 秒")
                del self.tree_build_state

            if hasattr(self.main_window, 'status_bar') and self.main_window.status_bar:
                self.main_window.status_bar.update_progress(100, "文件树构建完成")
                self.main_window.status_bar.disable_stop_button()

            # 计算文件统计信息
            self.calculate_file_stats()

        except Exception as e:
            self.logger.error(f"完成文件树构建时出错: {e}")

    def _batched_strict_layer_load_optimized(self, depth_map, all_depths, batch_size, cur_depth_idx, cur_index_in_layer, folder_nodes, created_roots, inserted_files):
        """优化的分层加载方法，减少UI阻塞"""
        if cur_depth_idx >= len(all_depths):
            # 加载完成
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(100, "文件树加载完成")
            self.logger.info("[文件树] 文件树加载完成！")
            return

        depth = all_depths[cur_depth_idx]
        layer_files = depth_map[depth]
        end_index = min(cur_index_in_layer + batch_size, len(layer_files))

        # 处理当前批次
        for i in range(cur_index_in_layer, end_index):
            try:
                # 检查中断
                if hasattr(self.main_window, 'interrupt_event') and self.main_window.interrupt_event.is_set():
                    self.logger.info("[文件树] 构建被中断")
                    return

                file_info = layer_files[i]
                file_path = file_info.get('file_path') or file_info.get('path') or file_info.get('filepath')
                normalized_path = _normalize_path(file_path)

                if not normalized_path or normalized_path in inserted_files:
                    continue

                # 跳过目录条目
                if file_info.get('is_dir') is True:
                    continue

                # 简化的文件插入
                self._insert_file_simple(normalized_path, file_info, folder_nodes, created_roots)
                inserted_files.add(normalized_path)

            except Exception as e:
                self.logger.error(f"处理文件失败: {e}")
                continue

        # 调度下一批次，使用更长的间隔
        if end_index < len(layer_files):
            # 本层未扫完，断点续扫
            self.frame.after(5, lambda: self._batched_strict_layer_load_optimized(
                depth_map, all_depths, batch_size, cur_depth_idx, end_index,
                folder_nodes, created_roots, inserted_files
            ))
        else:
            # 本层扫完，进入下一层
            self.frame.after(5, lambda: self._batched_strict_layer_load_optimized(
                depth_map, all_depths, batch_size, cur_depth_idx+1, 0,
                folder_nodes, created_roots, inserted_files
            ))

    def _insert_file_simple(self, file_path, file_info, folder_nodes, created_roots):
        """简化的文件插入方法"""
        try:
            root_dir = self._get_root_directory(file_path)

            # 创建根节点（如果不存在）
            if root_dir not in created_roots:
                root_folder_name = self._get_display_name_for_root(root_dir)
                root_node = self.tree.insert(
                    "", "end",
                    text=root_folder_name,
                    values=(root_folder_name, "", "根目录", "", root_dir),
                    tags=("folder", "root"),
                    open=False  # 修复：不自动展开根文件夹，避免触发大量懒加载
                )
                folder_nodes[root_dir] = root_node
                created_roots.add(root_dir)

            # 简化路径处理，直接插入到根目录下
            self.add_file_to_tree(folder_nodes[root_dir], file_path, file_info)

        except Exception as e:
            self.logger.error(f"简化插入文件失败 {file_path}: {e}")

    def _batched_strict_layer_load(self, depth_map, all_depths, batch_size, cur_depth_idx, cur_index_in_layer, folder_nodes, created_roots, inserted_files):
        if cur_depth_idx >= len(all_depths):
            # 加载完成
            for item in self.tree.get_children(""):
                self._log_folder_children(item, level=0)
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(100, "文件树加载完成")
            self.logger.info("[文件树] 文件树加载完成！")
            return
        depth = all_depths[cur_depth_idx]
        layer_files = depth_map[depth]
        end_index = min(cur_index_in_layer + batch_size, len(layer_files))
        count_this_batch = 0
        for i in range(cur_index_in_layer, end_index):
            file_info = layer_files[i]
            file_path = file_info.get('file_path') or file_info.get('path') or file_info.get('filepath')
            normalized_path = _normalize_path(file_path)
            # 检查路径有效性，但允许根目录（basename为空的情况）
            if not normalized_path:
                self.logger.error(f'[文件树] 跳过file_path为空的文件: {file_info}')
                continue

            # 对于根目录，basename可能为空，这是正常的
            basename = os.path.basename(normalized_path)
            if not basename and not self._is_root_directory(normalized_path):
                self.logger.error(f'[文件树] 跳过无效路径的文件: {file_info}')
                continue
            try:
                if normalized_path in inserted_files:
                    self.logger.warning(f'[文件树] 跳过重复插入文件: {normalized_path}')
                    continue

                # 检查是否为目录，如果是目录则跳过（目录应该在文件夹创建阶段处理）
                if file_info.get('is_dir') is True:
                    self.logger.debug(f'[文件树] 跳过目录条目，应在文件夹创建阶段处理: {normalized_path}')
                    continue

                root_dir = self._get_root_directory(normalized_path)
                if root_dir not in created_roots:
                    root_folder_name = self._get_display_name_for_root(root_dir)
                    root_node = self.tree.insert(
                        "", "end",
                        text=root_folder_name,
                        values=(root_folder_name, "", "根目录", "", root_dir),
                        tags=("folder", "root"),
                        open=False  # 修复：不自动展开根文件夹，避免触发大量懒加载
                    )
                    folder_nodes[root_dir] = root_node
                    created_roots.add(root_dir)
                    self.logger.info(f"[文件树] 插入根节点: {root_folder_name} ({root_dir})")
                try:
                    # 确保路径格式一致
                    normalized_root = root_dir.replace('\\', '/')
                    normalized_file = normalized_path.replace('\\', '/')
                    relative_path = os.path.relpath(normalized_file, normalized_root)
                except ValueError:
                    relative_path = normalized_path
                relative_path = relative_path.replace('\\', '/')
                path_parts = relative_path.split('/')
                if len(path_parts) == 1:
                    self.add_file_to_tree(folder_nodes[root_dir], normalized_path, file_info)
                    inserted_files.add(normalized_path)
                    count_this_batch += 1
                    continue
                current_parent = folder_nodes[root_dir]
                current_path = root_dir
                for folder_name in path_parts[:-1]:
                    if not folder_name:
                        continue
                    full_folder_path = os.path.join(current_path, folder_name).replace('\\', '/')
                    if full_folder_path not in folder_nodes:
                        try:
                            # 简化显示名称生成，避免复杂的重复检测
                            display_name = os.path.basename(full_folder_path) or "根目录"

                            # 如果显示名称为空（根目录情况），使用路径的最后部分
                            if not display_name or display_name == "根目录":
                                if full_folder_path.endswith(':') or full_folder_path.endswith(':/'):
                                    # Windows根目录，如 E: 或 E:/
                                    display_name = full_folder_path.rstrip('/') + "盘"
                                else:
                                    display_name = full_folder_path.split('/')[-1] or "根目录"

                            folder_node = self.tree.insert(
                                current_parent, "end",
                                text=display_name,
                                values=(display_name, "", "文件夹", "", full_folder_path),
                                tags=("folder", "directory"),
                                open=False
                            )
                            folder_nodes[full_folder_path] = folder_node
                            self.logger.info(f"[文件树] 插入文件夹节点: {display_name} ({full_folder_path}), 父节点: {current_path}")
                        except Exception as e:
                            self.logger.error(f"创建文件夹节点时出错: {str(e)}")
                            # 使用文件夹名称作为备用显示名称
                            fallback_display_name = os.path.basename(full_folder_path) or "未知文件夹"
                            folder_node = self.tree.insert(
                                current_parent, "end",
                                text=fallback_display_name,
                                values=(fallback_display_name, "", "文件夹", "", full_folder_path),
                                tags=("folder",)
                            )
                            folder_nodes[full_folder_path] = folder_node
                    current_parent = folder_nodes[full_folder_path]
                    current_path = full_folder_path
                self.add_file_to_tree(current_parent, normalized_path, file_info)
                inserted_files.add(normalized_path)
                count_this_batch += 1
            except Exception as e:
                self.logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                continue
        # 判断是否需要进入下一层或断点续扫
        if end_index < len(layer_files):
            # 本层未扫完，断点续扫
            self.frame.after(1, lambda: self._batched_strict_layer_load(depth_map, all_depths, batch_size, cur_depth_idx, end_index, folder_nodes, created_roots, inserted_files))
        else:
            # 本层扫完，进入下一层
            self.frame.after(1, lambda: self._batched_strict_layer_load(depth_map, all_depths, batch_size, cur_depth_idx+1, 0, folder_nodes, created_roots, inserted_files))

    def _log_folder_children(self, item, level=0):
        children = self.tree.get_children(item)
        values = self.tree.item(item, 'values')
        indent = '  ' * level
        self.logger.info(f"{indent}[文件树] 节点: {values[0] if values else item}, 子节点数: {len(children)}")
        for child in children:
            self._log_folder_children(child, level+1)

    def _insert_file_node(self, file, parent=''):
        try:
            file_path = file.get('file_path') or file.get('path') or file.get('filepath')
            if not file_path:
                self.logger.error(f'[文件树] 跳过插入file_path为空的文件节点: {file}')
                return
            
            # 使用全局的_normalize_path函数进行路径标准化
            original_path = file_path
            file_path = _normalize_path(file_path)
            
            # 添加路径标准化调试日志
            if original_path != file_path:
                self.logger.debug(f'[文件树] 插入节点路径标准化: {original_path} -> {file_path}')
            
            file['file_path'] = file_path
            file_name = os.path.basename(file_path)
            file_size = format_size(file.get("size", 0)) if isinstance(file, dict) else ""
            file_type = file.get("extension", "").upper() if isinstance(file, dict) else ""
            file_date = file.get("modified_time", "") if isinstance(file, dict) else ""
            whitelist_type_value = file.get("whitelist_type", file.get("file_type", "")) if isinstance(file, dict) else ""
            file_id = file.get("file_id", "") if isinstance(file, dict) else ""
            tags = self.get_file_tags(file)
            self.logger.debug(f"[文件树] 实际插入文件节点: {file_name}, parent={parent}, file_id={file_id}, path={file_path}")
            self.tree.insert(
                parent, "end",
                text=file_name,
                values=(file_name, file_size, file_type, file_date, file_path, whitelist_type_value, file_id),
                tags=tags,
            )
        except Exception as e:
            self.logger.error(f"插入文件节点时出错: {str(e)}")

    def build_tree_structure(self, files):
        if not files:
            return
        
        # 清理之前的插入记录
        if hasattr(self, '_inserted_file_paths'):
            self._inserted_file_paths.clear()
        
        self.tree.tag_configure("default", foreground="black")
        folder_nodes = {}
        sorted_files = sorted(files.items(), key=lambda x: x[0])
        created_roots = set()
        inserted_files = set()
        
        for file_path, file_info in sorted_files:
            normalized_path = _normalize_path(file_path)
            # 新增：严格过滤空路径和无效路径
            if not normalized_path:
                self.logger.error(f'[文件树] 跳过file_path为空的文件: {file_info}')
                continue

            # 对于根目录，basename可能为空，这是正常的
            basename = os.path.basename(normalized_path)
            if not basename and not self._is_root_directory(normalized_path):
                self.logger.error(f'[文件树] 跳过无效路径的文件: {file_info}')
                continue
            try:
                # 检查文件是否已经插入
                if normalized_path in inserted_files:
                    self.logger.warning(f'[文件树] build_tree_structure: 跳过重复插入文件: {normalized_path}')
                    continue
                
                root_dir = self._get_root_directory(normalized_path)
                
                # 创建根节点
                if root_dir not in created_roots:
                    root_folder_name = self._get_display_name_for_root(root_dir)
                    root_node = self.tree.insert(
                        "", "end",
                        text=root_folder_name,
                        values=(root_folder_name, "", "根目录", "", root_dir),
                        tags=("folder", "root"),
                        open=False  # 修复：不自动展开根文件夹，避免触发大量懒加载
                    )
                    folder_nodes[root_dir] = root_node
                    created_roots.add(root_dir)
                    self.logger.info(f"[文件树] 插入根节点: {root_folder_name} ({root_dir})")
                
                # 计算相对路径
                try:
                    relative_path = os.path.relpath(normalized_path, root_dir)
                except ValueError:
                    relative_path = normalized_path
                
                relative_path = relative_path.replace('\\', '/')
                path_parts = relative_path.split('/')
                
                # 如果文件在根目录下
                if len(path_parts) == 1:
                    self.add_file_to_tree(folder_nodes[root_dir], normalized_path, file_info)
                    inserted_files.add(normalized_path)
                    continue
                
                # 构建文件夹层级
                current_parent = folder_nodes[root_dir]
                current_path = root_dir
                
                for folder_name in path_parts[:-1]:
                    if not folder_name:
                        continue
                    
                    full_folder_path = os.path.join(current_path, folder_name).replace('\\', '/')
                    
                    # 如果文件夹节点不存在，创建它
                    if full_folder_path not in folder_nodes:
                        try:
                            # 获取唯一的显示名称
                            display_name = self._get_unique_display_name(full_folder_path, folder_nodes, root_dir)
                            
                            # 如果检测到重复，更新所有相关的显示名称
                            if self._has_duplicate_folder_name(folder_name, folder_nodes, current_path):
                                self._update_existing_display_names(folder_name, folder_nodes)
                                # 重新获取显示名称
                                display_name = self._get_unique_display_name(full_folder_path, folder_nodes, root_dir)
                            
                            folder_node = self.tree.insert(
                                current_parent, "end",
                                text=display_name,
                                values=(display_name, "", "文件夹", "", full_folder_path),
                                tags=("folder", "directory"),
                                open=False
                            )
                            folder_nodes[full_folder_path] = folder_node
                            self.logger.info(f"[文件树] 插入文件夹节点: {display_name} ({full_folder_path}), 父节点: {current_path}")
                        except Exception as e:
                            self.logger.error(f"创建文件夹节点时出错: {str(e)}")
                            # 使用文件夹名称作为备用显示名称
                            fallback_display_name = os.path.basename(full_folder_path) or "未知文件夹"
                            folder_node = self.tree.insert(
                                current_parent, "end",
                                text=fallback_display_name,
                                values=(fallback_display_name, "", "文件夹", "", full_folder_path),
                                tags=("folder",)
                            )
                            folder_nodes[full_folder_path] = folder_node
                    
                    # 更新当前父节点和路径
                    current_parent = folder_nodes[full_folder_path]
                    current_path = full_folder_path
                
                # 添加文件到最终的父节点
                self.add_file_to_tree(current_parent, normalized_path, file_info)
                inserted_files.add(normalized_path)
                
            except Exception as e:
                self.logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                continue
    
    def _get_root_directory(self, file_path):
        """
        获取文件的根目录
        
        参数:
            file_path (str): 文件路径
            
        返回:
            str: 根目录路径
        """
        try:
            # 规范化路径
            normalized_path = os.path.normpath(file_path)
            
            # 获取驱动器根目录（Windows）或根目录（Unix）
            if os.name == 'nt':  # Windows
                # 获取驱动器根目录
                drive = os.path.splitdrive(normalized_path)[0]
                if drive:
                    # 统一使用正斜杠格式
                    return drive + "/"
                else:
                    # 网络路径或其他情况
                    return os.path.dirname(normalized_path).replace('\\', '/')
            else:  # Unix/Linux
                # 获取根目录
                return "/"
        except Exception as e:
            self.logger.error(f"获取根目录失败: {file_path}, 错误: {e}")
            # 返回文件所在目录作为备选
            return os.path.dirname(file_path)

    def _is_root_directory(self, path):
        """判断是否为根目录"""
        if not path:
            return False

        # 规范化路径
        normalized_path = path.replace('\\', '/').rstrip('/')

        # Windows根目录：C:, D:, E: 等
        if os.name == 'nt':
            # 匹配 C:, D:, E: 等格式
            import re
            return bool(re.match(r'^[A-Za-z]:$', normalized_path))
        else:
            # Unix/Linux根目录
            return normalized_path == '' or normalized_path == '/'

    def _has_duplicate_folder_name(self, folder_name, folder_nodes, current_parent_path=""):
        """
        优化的重复文件夹名称检测
        只检查同一父目录下的重复，减少日志噪音

        参数:
            folder_name (str): 文件夹名称
            folder_nodes (dict): 已创建的文件夹节点字典
            current_parent_path (str): 当前父目录路径

        返回:
            bool: 是否有重复名称
        """
        import time
        from collections import defaultdict

        # 初始化缓存（如果还没有）
        if not hasattr(self, 'folder_name_cache'):
            self.folder_name_cache = defaultdict(set)
        if not hasattr(self, 'duplicate_stats'):
            self.duplicate_stats = defaultdict(int)
        if not hasattr(self, 'duplicate_details'):
            self.duplicate_details = defaultdict(list)
        if not hasattr(self, 'last_duplicate_log_time'):
            self.last_duplicate_log_time = {}

        normalized_folder_name = folder_name.lower()

        # 检查同一父目录下是否已有同名文件夹
        existing_folders = self.folder_name_cache[current_parent_path]
        is_duplicate = normalized_folder_name in existing_folders

        if is_duplicate:
            # 更新统计
            self.duplicate_stats[folder_name] += 1
            full_path = os.path.join(current_parent_path, folder_name) if current_parent_path else folder_name
            self.duplicate_details[folder_name].append(full_path)

            # 限制日志频率（每个文件夹名称最多每30秒记录一次）
            current_time = time.time()
            last_log_time = self.last_duplicate_log_time.get(folder_name, 0)

            if current_time - last_log_time > 30:  # 30秒间隔
                count = self.duplicate_stats[folder_name]
                if count <= 5:
                    # 少量重复，使用debug级别
                    self.logger.debug(f"发现重复文件夹: {folder_name} (父目录: {current_parent_path}, 第{count}次)")
                elif count <= 20:
                    # 中等重复，使用info级别
                    self.logger.info(f"发现较多重复文件夹: {folder_name} (已出现{count}次)")
                else:
                    # 大量重复，使用warning级别，但频率更低
                    if current_time - last_log_time > 120:  # 2分钟间隔
                        self.logger.warning(f"发现大量重复文件夹: {folder_name} (已出现{count}次)")
                        self.last_duplicate_log_time[folder_name] = current_time

                if count <= 20:  # 只有中等重复以下才更新时间
                    self.last_duplicate_log_time[folder_name] = current_time

        # 更新缓存
        self.folder_name_cache[current_parent_path].add(normalized_folder_name)

        return is_duplicate

    def _report_duplicate_folder_summary(self):
        """
        输出重复文件夹汇总报告
        在文件树构建完成后调用，减少日志噪音
        """
        try:
            if not hasattr(self, 'duplicate_stats') or not self.duplicate_stats:
                self.logger.debug("✅ 未发现重复文件夹名称")
                return

            total_duplicates = len(self.duplicate_stats)
            total_occurrences = sum(self.duplicate_stats.values())

            self.logger.info(f"📊 重复文件夹统计报告:")
            self.logger.info(f"   发现 {total_duplicates} 种重复文件夹名称，共 {total_occurrences} 次重复")

            # 按重复次数排序，只显示前10个最频繁的
            sorted_duplicates = sorted(self.duplicate_stats.items(), key=lambda x: x[1], reverse=True)

            for i, (folder_name, count) in enumerate(sorted_duplicates[:10]):
                self.logger.info(f"   📁 #{i+1} '{folder_name}': {count} 次重复")

                # 显示部分路径示例（debug级别）
                paths = self.duplicate_details.get(folder_name, [])
                if len(paths) <= 3:
                    for path in paths:
                        self.logger.debug(f"      - {path}")
                else:
                    for path in paths[:2]:
                        self.logger.debug(f"      - {path}")
                    self.logger.debug(f"      - ... 还有 {len(paths) - 2} 个路径")

            if total_duplicates > 10:
                self.logger.info(f"   ... 还有 {total_duplicates - 10} 种重复文件夹名称")

            # 给出建议
            if total_duplicates > 50:
                self.logger.warning(f"⚠️  发现大量重复文件夹名称，建议检查文件组织结构")
            elif total_duplicates > 20:
                self.logger.info(f"💡 发现较多重复文件夹名称，这在大型项目中是正常的")

        except Exception as e:
            self.logger.error(f"生成重复文件夹汇总报告失败: {e}")

    def _clear_duplicate_cache(self):
        """
        清理重复检测缓存
        在文件树构建完成后调用
        """
        try:
            if hasattr(self, 'folder_name_cache'):
                self.folder_name_cache.clear()
            if hasattr(self, 'duplicate_stats'):
                self.duplicate_stats.clear()
            if hasattr(self, 'duplicate_details'):
                self.duplicate_details.clear()
            if hasattr(self, 'last_duplicate_log_time'):
                self.last_duplicate_log_time.clear()

            self.logger.debug("重复检测缓存已清理")

        except Exception as e:
            self.logger.error(f"清理重复检测缓存失败: {e}")

    def _get_unique_display_name(self, folder_path, folder_nodes, root_dir):
        """
        获取唯一的文件夹显示名称
        
        参数:
            folder_path (str): 文件夹路径
            folder_nodes (dict): 已创建的文件夹节点字典
            root_dir (str): 根目录路径
            
        返回:
            str: 唯一的显示名称
        """
        # 规范化路径
        normalized_path = _normalize_path(folder_path)
        folder_name = os.path.basename(normalized_path)
        
        # 检查是否有重复的文件夹名称
        parent_path = os.path.dirname(normalized_path)
        has_duplicate = self._has_duplicate_folder_name(folder_name, folder_nodes, parent_path)
        
        if has_duplicate:
            # 计算相对于根目录的路径
            try:
                # 确保根目录使用正斜杠
                normalized_root = _normalize_path(root_dir)
                if normalized_path.startswith(normalized_root):
                    # 创建相对路径，保留所有父目录结构
                    relative_path = normalized_path[len(normalized_root):].lstrip('/')
                    # 返回更详细的路径作为显示名称
                    return relative_path
                else:
                    # 如果不是根目录的子路径，使用完整路径的最后两部分
                    path_parts = normalized_path.split('/')
                    if len(path_parts) >= 3:
                        # 使用最后两个父文件夹名称和文件夹名称
                        return '/'.join(path_parts[-3:])
                    elif len(path_parts) >= 2:
                        # 使用最后一个父文件夹名称和文件夹名称
                        return '/'.join(path_parts[-2:])
                    else:
                        # 没有父文件夹，返回带路径的名称
                        return normalized_path
            except Exception as e:
                self.logger.error(f"生成唯一文件夹显示名称时出错: {e}")
                # 出错时返回原始名称
                return folder_name
        else:
            # 没有重复，直接返回文件夹名称
            return folder_name

    def _update_existing_display_names(self, folder_name, folder_nodes):
        """
        更新已存在的重复文件夹显示名称
        
        参数:
            folder_name (str): 重复的文件夹名称
            folder_nodes (dict): 已创建的文件夹节点字典
        """
        # 规范化文件夹名称（大小写不敏感）
        normalized_folder_name = folder_name.lower()
        
        # 找到所有具有相同名称的文件夹
        duplicate_paths = []
        for path, node_id in folder_nodes.items():
            normalized_path = _normalize_path(path)
            path_basename = os.path.basename(normalized_path).lower()
            if path_basename == normalized_folder_name:
                duplicate_paths.append((normalized_path, node_id))
        
        # 为所有重复的文件夹更新显示名称
        for path, node_id in duplicate_paths:
            path_parts = path.split('/')
            
            # 根据路径长度生成显示名称
            if len(path_parts) >= 3:
                # 使用最后三个部分作为显示名称
                new_display_name = '/'.join(path_parts[-3:])
            elif len(path_parts) >= 2:
                # 使用最后两个部分作为显示名称
                new_display_name = '/'.join(path_parts[-2:])
            else:
                # 使用完整路径作为显示名称
                new_display_name = path
            
            try:
                # 更新树节点中的显示名称
                self.tree.item(node_id, text=new_display_name)
                # 同时更新values中的第一个值(显示名称)
                values = self.tree.item(node_id, 'values')
                if values and len(values) >= 5:
                    new_values = list(values)
                    new_values[0] = new_display_name  # 更新显示名称
                    self.tree.item(node_id, values=tuple(new_values))
            except Exception as e:
                self.logger.error(f"更新节点 {node_id} 的显示名称时出错: {e}")

    def _get_display_name_for_root(self, root_dir):
        """
        获取根目录的显示名称
        
        参数:
            root_dir (str): 根目录路径
            
        返回:
            str: 显示名称
        """
        try:
            if os.name == 'nt':  # Windows
                # Windows驱动器根目录
                if root_dir.endswith("\\"):
                    drive_letter = root_dir[0]
                    return f"{drive_letter}:\\"
                else:
                    # 网络路径或其他情况
                    return os.path.basename(root_dir) or root_dir
            else:  # Unix/Linux
                # Unix根目录
                if root_dir == "/":
                    return "/"
                else:
                    return os.path.basename(root_dir) or root_dir
        except Exception as e:
            self.logger.error(f"获取根目录显示名称失败: {root_dir}, 错误: {e}")
            return root_dir
    
    def add_file_to_tree(self, parent, file_path, file_info):
        """
        向树中添加文件节点
        
        参数:
            parent: 父节点ID
            file_path: 文件路径
            file_info: 文件信息
        """
        try:
            # 规范化路径，确保一致性
            normalized_path = _normalize_path(file_path)
            # 检查路径有效性
            if not normalized_path:
                self.logger.error(f'[文件树] 跳过插入file_path为空的文件节点: {file_info}')
                return

            # 对于根目录，basename可能为空，这是正常的
            basename = os.path.basename(normalized_path)
            if not basename and not self._is_root_directory(normalized_path):
                self.logger.error(f'[文件树] 跳过插入无效路径的文件节点: {file_info}')
                return
            # 新增：如果是文件夹(is_dir为True)则不插入为文件节点
            if file_info.get('is_dir') is True:
                self.logger.info(f'[文件树] 跳过将文件夹作为文件节点插入: {file_info}')
                return
            
            # 检查文件是否已经存在于指定父节点下
            is_duplicate = False
            for child_id in self.tree.get_children(parent):
                child_values = self.tree.item(child_id, 'values')
                if child_values and len(child_values) >= 5:
                    child_path = _normalize_path(child_values[4])
                    # 大小写不敏感比较
                    if child_path.lower() == normalized_path.lower():
                        self.logger.warning(f"[文件树] add_file_to_tree: 跳过重复插入文件: {normalized_path}")
                        is_duplicate = True
                        break
            
            if is_duplicate:
                return
            
            # 提取文件名
            file_name = os.path.basename(normalized_path)
            
            # 获取文件类型、大小和修改时间
            try:
                file_size = format_size(file_info.get("size", 0)) if isinstance(file_info, dict) else ""
                file_type = file_info.get("extension", "").upper() if isinstance(file_info, dict) else ""
                file_date = file_info.get("modified_time", "") if isinstance(file_info, dict) else ""
                whitelist_type_value = file_info.get("whitelist_type", file_info.get("file_type", "")) if isinstance(file_info, dict) else ""
                file_id = file_info.get("file_id", "") if isinstance(file_info, dict) else ""
            except Exception as e:
                self.logger.warning(f"文件信息不完整: {e}")
                file_size = ""
                file_type = os.path.splitext(file_name)[1].upper()[1:] if file_name else ""
                file_date = ""
                whitelist_type_value = ""
                file_id = ""
            
            # 获取文件标签
            tags = self.get_file_tags(file_info)
            
            # 添加文件节点 - 保持与原始代码兼容的values元组结构
            node_id = self.tree.insert(
                parent, "end",
                text=file_name,
                values=(file_name, file_size, file_type, file_date, normalized_path, whitelist_type_value, file_id),
                tags=tags
            )
            
            # 记录已插入的路径
            if hasattr(self, '_inserted_file_paths'):
                self._inserted_file_paths.add(normalized_path.lower())
                
            self.logger.info(f"[文件树] 已插入文件节点: {file_name} ({normalized_path}), 父节点: {parent}")
            return node_id
            
        except Exception as e:
            self.logger.error(f"添加文件到树失败: {e}, 文件路径: {file_path}")
            return None
    
    def apply_filters(self, files):
        """
        应用过滤器
        
        参数:
            files (dict): 文件信息字典
        
        返回:
            dict: 过滤后的文件信息字典
        """
        if not files:
            return {}
        
        # 获取过滤条件
        search_text = self.search_var.get().lower()
        file_type = self.file_type_var.get()
        file_size = self.file_size_var.get()
        
        # 过滤文件
        filtered_files = {}
        for file_path, file_info in files.items():
            # 文件名过滤
            file_name = os.path.basename(file_path).lower()
            if search_text and search_text not in file_name and search_text not in file_path.lower():
                continue
            
            # 文件类型过滤
            extension = file_info.get("extension", "").lower()
            if file_type != "所有文件":
                if file_type == "视频文件" and extension not in [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm"]:
                    continue
                elif file_type == "图片文件" and extension not in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"]:
                    continue
                elif file_type == "音频文件" and extension not in [".mp3", ".wav", ".ogg", ".flac", ".aac", ".wma"]:
                    continue
                elif file_type == "文档文件" and extension not in [".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"]:
                    continue
                elif file_type == "压缩文件" and extension not in [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2"]:
                    continue
                elif file_type == "其他文件" and extension in [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".mp3", ".wav", ".ogg", ".flac", ".aac", ".wma", ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2"]:
                    continue
            
            # 文件大小过滤
            size = file_info.get("size", 0)
            if file_size != "所有大小":
                if file_size == "< 1MB" and size >= 1024 * 1024:
                    continue
                elif file_size == "1MB - 10MB" and (size < 1024 * 1024 or size >= 10 * 1024 * 1024):
                    continue
                elif file_size == "10MB - 100MB" and (size < 10 * 1024 * 1024 or size >= 100 * 1024 * 1024):
                    continue
                elif file_size == "100MB - 1GB" and (size < 100 * 1024 * 1024 or size >= 1024 * 1024 * 1024):
                    continue
                elif file_size == "> 1GB" and size < 1024 * 1024 * 1024:
                    continue
            
            # 添加到过滤后的文件字典
            filtered_files[file_path] = file_info
        
        return filtered_files
    
    def get_file_tags(self, file_info):
        """
        获取文件标签
        
        参数:
            file_info (dict): 文件信息
        
        返回:
            tuple: 标签元组
        """
        try:
            tags = ['file']  # 默认添加'file'标签
            
            # 获取文件扩展名
            extension = file_info.get('extension', '').lower() if isinstance(file_info, dict) else ''
            
            # 根据文件类型添加标签
            if extension in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm']:
                tags.append('video')
            elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']:
                tags.append('image')
            elif extension in ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.wma']:
                tags.append('audio')
            elif extension in ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.xls', '.xlsx', '.ppt', '.pptx']:
                tags.append('document')
            elif extension in ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']:
                tags.append('archive')
            
            # 添加特殊标签
            if isinstance(file_info, dict):
                if file_info.get('is_junk', False):
                    tags.append('junk')
                if file_info.get('is_whitelist', False):
                    tags.append('whitelist')
                if file_info.get('is_duplicate', False):
                    tags.append('duplicate')
            
            return tuple(tags)
            
        except Exception as e:
            self.logger.error(f"生成文件标签时出错: {str(e)}")
            return ('file',)  # 返回默认标签
    
    def load_files(self, files):
        """
        加载文件列表到文件树
        
        参数:
            files (list): 文件信息列表
        """
        try:
            # 将文件列表转换为字典格式
            files_dict = {}
            for file_info in files:
                file_path = file_info.get('path', '')
                if file_path:
                    files_dict[file_path] = {
                        'size': file_info.get('size', 0),
                        'extension': os.path.splitext(file_path)[1],
                        'modified_time': file_info.get('modified_time', ''),
                        # 跳过自动垃圾文件和白名单检查，使用数据库中的现有值或默认值
                        'is_junk': file_info.get('is_junk', False),
                        'is_whitelist': file_info.get('is_whitelist', False),
                        'whitelist_type': file_info.get('whitelist_type', file_info.get('file_type', ''))
                    }
            
            # 更新文件树
            self.update_file_tree({'files': files_dict, 'directory': ''})

            self.main_window.log_message(f'已加载 {len(files)} 个文件到文件树（跳过自动垃圾文件和白名单检查）', 'info')
            
        except Exception as e:
            self.main_window.log_message(f'加载文件到文件树失败: {str(e)}', 'error')
            self.logger.error(f'加载文件到文件树失败: {str(e)}')
    
    def sort_tree_column(self, column, toggle=True):
        """
        排序树形视图列
        
        参数:
            column (str): 列名
            toggle (bool): 是否切换排序顺序
        """
        # 更新排序列和排序顺序
        if toggle and self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False
        
        # 获取所有项目
        items = [(self.tree.set(item, column), item) for item in self.tree.get_children("")]
        
        # 根据列类型进行排序
        if column == "size":
            # 将大小转换为字节数进行排序
            def size_to_bytes(size_str):
                if "B" in size_str and not "KB" in size_str and not "MB" in size_str and not "GB" in size_str:
                    return float(size_str.split(" ")[0])
                elif "KB" in size_str:
                    return float(size_str.split(" ")[0]) * 1024
                elif "MB" in size_str:
                    return float(size_str.split(" ")[0]) * 1024 * 1024
                elif "GB" in size_str:
                    return float(size_str.split(" ")[0]) * 1024 * 1024 * 1024
                return 0
            items = [(size_to_bytes(item[0]), item[1]) for item in items]
        elif column == "date":
            # 将日期转换为时间戳进行排序
            def date_to_timestamp(date_str):
                try:
                    return time.mktime(time.strptime(date_str, "%Y-%m-%d %H:%M:%S"))
                except:
                    return 0
            items = [(date_to_timestamp(item[0]), item[1]) for item in items]
        elif column == "whitelist_type":
            items = [(str(item[0]), item[1]) for item in items]
        # 排序
        items.sort(reverse=(self.sort_reverse))
        
        # 重新排列项目
        for index, (_, item) in enumerate(items):
            self.tree.move(item, "", index)
        
        # 更新列标题
        for col in ("name", "size", "type", "date", "path", "whitelist_type"):
            self.tree.heading(col, text=self.tree.heading(col, "text").replace(" ▲", "").replace(" ▼", ""))
        
        # 添加排序指示器
        self.tree.heading(column, text=f"{self.tree.heading(column, 'text')} {'▲' if self.sort_reverse else '▼'}")
    
    def filter_tree(self):
        """
        过滤树形视图
        """
        # 更新过滤文本
        self.filter_text = self.search_var.get().lower()
        
        # 重新填充树形视图
        self.clear_tree()
        self.populate_tree(self.files)
    
    def clear_filter(self):
        """
        清除过滤器
        """
        # 清除搜索文本
        self.search_var.set("")
        self.filter_text = ""
        
        # 重置文件类型和大小过滤器
        self.file_type_var.set("所有文件")
        self.file_size_var.set("所有大小")
        
        # 重新填充树形视图
        self.clear_tree()
        self.populate_tree(self.files)
    
    def refresh_file_tree(self):
        """刷新文件树"""
        try:
            # 记录刷新开始日志
            self.logger.info("开始刷新文件树...")
            
            # 调用主窗口的刷新方法
            if hasattr(self.main_window, 'refresh_file_tree') and callable(getattr(self.main_window, 'refresh_file_tree')):
                self.logger.info("通过主窗口的refresh_file_tree方法刷新文件树")
                self.main_window.refresh_file_tree()
            elif hasattr(self.main_window, 'load_all_files_from_database') and callable(getattr(self.main_window, 'load_all_files_from_database')):
                # 直接调用数据库加载方法，这是更可靠的刷新方式
                self.logger.info("通过主窗口的load_all_files_from_database方法刷新文件树")
                self.main_window.load_all_files_from_database()
            else:
                # 如果没有主窗口的刷新方法，重新加载当前数据
                self.logger.info("无法通过主窗口刷新，仅重新加载当前数据")
                self.clear_tree()
                self.populate_tree(self.files)
                self.logger.info("文件树已重新加载现有数据")
            
            # 显示刷新完成消息
            messagebox.showinfo("刷新完成", "文件树已刷新")
                
        except Exception as e:
            self.logger.error(f"刷新文件树失败: {e}")
            messagebox.showerror("错误", f"刷新文件树失败: {e}")

    def manual_check_whitelist(self):
        """手动检查白名单状态"""
        try:
            if not self.files:
                messagebox.showinfo("提示", "没有文件需要检查白名单状态")
                return

            # 询问用户是否确认
            response = messagebox.askyesno(
                "确认操作",
                f"即将检查 {len(self.files)} 个文件的白名单状态，这可能需要一些时间。\n\n是否继续？"
            )

            if not response:
                return

            self.logger.info(f"开始手动检查 {len(self.files)} 个文件的白名单状态")

            # 临时启用自动白名单检查
            original_auto_check = self.auto_whitelist_check
            self.auto_whitelist_check = True

            def after_whitelist_refresh():
                # 恢复原始设置
                self.auto_whitelist_check = original_auto_check

                # 刷新文件树显示
                self.clear_tree()
                self.populate_tree(self.files)
                self.calculate_file_stats()

                self.logger.info("手动白名单检查完成")
                messagebox.showinfo("完成", "白名单状态检查完成！")

            # 执行白名单检查
            file_list = list(self.files.values())
            if len(file_list) > 200 and self.concurrent_refresh_enabled:
                self.logger.info(f"文件量较大({len(file_list)}个)，使用并发刷新")
                self._refresh_whitelist_status_concurrent(file_list, batch_size=100, on_complete=after_whitelist_refresh)
            else:
                self.logger.info(f"文件量较小({len(file_list)}个)，使用普通批量刷新")
                self._refresh_whitelist_status_in_batches(file_list, batch_size=100, on_complete=after_whitelist_refresh)

        except Exception as e:
            self.logger.error(f"手动检查白名单失败: {e}")
            messagebox.showerror("错误", f"手动检查白名单失败: {str(e)}")

    def on_tree_single_click(self, event):
        """
        处理树形视图单击事件
        
        参数:
            event: 事件对象
        """
        # 获取选中的项目
        item = self.tree.identify("item", event.x, event.y)
        if not item:
            # 清空文件信息显示
            self.file_name_var.set("")
            self.file_extension_var.set("")
            return
        
        # 获取项目信息
        values = self.tree.item(item, "values")
        tags = self.tree.item(item, "tags")
        
        if "folder" in tags:
            # 文件夹：显示文件夹名称
            folder_name = values[0] if values else ""
            self.file_name_var.set(folder_name)
            self.file_extension_var.set("文件夹")
        else:
            # 文件：显示文件名和后缀
            if values and len(values) >= 5:
                file_path = values[4]  # 文件路径在第5列
                if file_path:
                    file_name = os.path.basename(file_path)
                    file_base, file_ext = os.path.splitext(file_name)
                    
                    self.file_name_var.set(file_base)
                    self.file_extension_var.set(file_ext if file_ext else "无后缀")
                else:
                    self.file_name_var.set("")
                    self.file_extension_var.set("")
            else:
                self.file_name_var.set("")
                self.file_extension_var.set("")
    
    def on_tree_double_click(self, event):
        """
        处理树形视图双击事件
        
        参数:
            event: 事件对象
        """
        # 获取选中的项目
        item = self.tree.identify("item", event.x, event.y)
        if not item:
            return
        
        # 检查是否为文件夹
        tags = self.tree.item(item, "tags")
        if "folder" in tags:
            # 文件夹：切换展开/折叠状态
            if self.tree.item(item, "open"):
                self.tree.item(item, open=False)
            else:
                self.tree.item(item, open=True)
            return
        
        # 文件：获取文件路径并打开
        file_path = self.tree.item(item, "values")[-1]
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 打开文件
        self.open_file(file_path)
    
    def show_context_menu(self, event):
        """
        显示上下文菜单
        
        参数:
            event: 事件对象
        """
        # 获取选中的项目
        item = self.tree.identify("item", event.x, event.y)
        
        if not item:
            # 如果没有选中项目，显示简化的上下文菜单
            self.show_empty_context_menu(event)
            return
        
        # 选中项目
        self.tree.selection_set(item)
        
        # 显示完整的上下文菜单
        self.show_full_context_menu(event)
    
    def show_empty_context_menu(self, event):
        """
        显示空选中状态的上下文菜单
        
        参数:
            event: 事件对象
        """
        try:
            # 创建简化的上下文菜单
            empty_menu = tk.Menu(self.tree, tearoff=0)
            empty_menu.add_command(label="刷新数据库", command=self.refresh_file_tree)
            
            # 显示菜单
            empty_menu.post(event.x_root, event.y_root)
            
        except Exception as e:
            self.logger.error(f"显示空选中状态上下文菜单时出错: {str(e)}")
    
    def show_full_context_menu(self, event):
        """
        显示完整选中状态的上下文菜单
        
        参数:
            event: 事件对象
        """
        try:
            # 显示完整的上下文菜单
            self.context_menu.post(event.x_root, event.y_root)
            
        except Exception as e:
            self.logger.error(f"显示完整上下文菜单时出错: {str(e)}")
    
    def open_file(self, file_path=None):
        """
        打开文件
        
        参数:
            file_path (str): 文件路径，如果为None则使用选中的文件
        """
        # 获取文件路径
        if file_path is None:
            selected = self.tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个文件")
                return
            
            file_path = self.tree.item(selected[0], "values")[4]
        file_path = _normalize_path(file_path)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 使用系统默认程序打开文件
        try:
            if sys.platform == "win32":
                os.startfile(file_path)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open \"{file_path}\"")
            else:  # Linux
                os.system(f"xdg-open \"{file_path}\"")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件: {str(e)}")
            self.logger.error(f"无法打开文件: {file_path}, 错误: {str(e)}")
    
    def open_containing_folder(self):
        """
        打开文件所在文件夹
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_path = self.tree.item(selected[0], "values")[4]
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 获取文件所在目录
        folder_path = os.path.dirname(file_path)
        
        # 打开文件夹
        try:
            if sys.platform == "win32":
                os.startfile(folder_path)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open \"{folder_path}\"")
            else:  # Linux
                os.system(f"xdg-open \"{folder_path}\"")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")
            self.logger.error(f"无法打开文件夹: {folder_path}, 错误: {str(e)}")
    
    def copy_file_path(self):
        """
        复制文件路径到剪贴板
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_path = self.tree.item(selected[0], "values")[4]
        if not file_path:
            return
        
        # 复制到剪贴板
        self.main_window.root.clipboard_clear()
        self.main_window.root.clipboard_append(file_path)
        
        # 显示提示
        self.main_window.status_text.set(f"已复制文件路径: {file_path}")
    
    def delete_file(self):
        """
        删除文件
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_paths = [self.tree.item(item, "values")[4] for item in selected]
        
        # 确认删除
        if len(file_paths) == 1:
            if not messagebox.askyesno("确认删除", f"确定要删除文件 {os.path.basename(file_paths[0])} 吗？"):
                return
        else:
            if not messagebox.askyesno("确认删除", f"确定要删除选中的 {len(file_paths)} 个文件吗？"):
                return
        
        # 添加删除任务到队列
        self.main_window.task_queue.put({"type": "delete_files", "data": {"files": file_paths}})
    
    def rename_file(self):
        """
        重命名文件
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected or len(selected) != 1:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_path = self.tree.item(selected[0], "values")[4]
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 获取文件名和扩展名
        file_dir = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)
        file_base, file_ext = os.path.splitext(file_name)
        
        # 创建重命名对话框
        rename_dialog = tk.Toplevel(self.main_window.root)
        rename_dialog.title("重命名文件")
        rename_dialog.geometry("400x150")
        rename_dialog.resizable(False, False)
        rename_dialog.transient(self.main_window.root)
        rename_dialog.grab_set()
        
        # 创建对话框内容
        ttk.Label(rename_dialog, text="原文件名:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        ttk.Label(rename_dialog, text=file_name).grid(row=0, column=1, padx=10, pady=10, sticky="w")
        
        ttk.Label(rename_dialog, text="新文件名:").grid(row=1, column=0, padx=10, pady=10, sticky="w")
        new_name_var = tk.StringVar(value=file_base)
        new_name_entry = ttk.Entry(rename_dialog, textvariable=new_name_var, width=30)
        new_name_entry.grid(row=1, column=1, padx=10, pady=10, sticky="w")
        new_name_entry.select_range(0, len(file_base))
        new_name_entry.focus_set()
        
        # 创建按钮框架
        button_frame = ttk.Frame(rename_dialog)
        button_frame.grid(row=2, column=0, columnspan=2, padx=10, pady=10)
        
        # 确定按钮
        def do_rename():
            new_name = new_name_var.get()
            if not new_name:
                messagebox.showwarning("警告", "新文件名不能为空")
                return
            if re.search(r'[\\/:*?"<>|]', new_name):
                messagebox.showwarning("警告", "文件名不能包含以下字符: \\ / : * ? \" < > |")
                return
            new_file_path = _normalize_path(os.path.join(file_dir, new_name + file_ext))
            file_path_norm = _normalize_path(file_path)
            if os.path.exists(new_file_path) and new_file_path != file_path_norm:
                messagebox.showwarning("警告", f"文件已存在: {new_name + file_ext}")
                return
            try:
                os.rename(file_path_norm, new_file_path)
                self.main_window.log_message(f"重命名文件: {file_name} -> {new_name + file_ext}", "success")
                if self.main_window.db_manager:
                    self.main_window.db_manager.update_file_path(file_path_norm, new_file_path)
                self.main_window.event_system.publish("file_renamed", {
                    "file_path": file_path_norm,
                    "new_file_path": new_file_path
                })
                self.filter_tree()
                rename_dialog.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"重命名文件失败: {str(e)}")
                self.logger.error(f"重命名文件失败: {file_path_norm} -> {new_file_path}, 错误: {str(e)}")
        
        ttk.Button(button_frame, text="确定", command=do_rename).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=rename_dialog.destroy).pack(side=tk.LEFT, padx=10)
        
        # 绑定回车键
        rename_dialog.bind("<Return>", lambda e: do_rename())
        rename_dialog.bind("<Escape>", lambda e: rename_dialog.destroy())
    
    def add_to_whitelist(self):
        """
        添加文件到白名单
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_paths = [self.tree.item(item, "values")[4] for item in selected]
        
        # 确认添加
        if len(file_paths) == 1:
            if not messagebox.askyesno("确认添加", f"确定要将文件 {os.path.basename(file_paths[0])} 添加到白名单吗？"):
                return
        else:
            if not messagebox.askyesno("确认添加", f"确定要将选中的 {len(file_paths)} 个文件添加到白名单吗？"):
                return
        
        # 添加到白名单
        try:
            for file_path in file_paths:
                # 更新数据库
                if self.main_window.db_manager:
                    self.main_window.db_manager.update_file_info(file_path, {"is_whitelist": True, "is_junk": False, "whitelist_type": "CH"})
                # 更新文件信息
                if file_path in self.files:
                    self.files[file_path]["is_whitelist"] = True
                    self.files[file_path]["is_junk"] = False
                    self.files[file_path]["whitelist_type"] = "CH"
            # 刷新文件树
            self.filter_tree()
            # 显示提示
            if len(file_paths) == 1:
                self.main_window.log_message(f"已将文件 {os.path.basename(file_paths[0])} 添加到白名单", "success")
            else:
                self.main_window.log_message(f"已将 {len(file_paths)} 个文件添加到白名单", "success")
        except Exception as e:
            messagebox.showerror("错误", f"添加到白名单失败: {str(e)}")
            self.logger.error(f"添加到白名单失败, 错误: {str(e)}")
    
    def mark_as_junk(self):
        """
        标记文件为垃圾文件
        """
        # 获取选中的文件
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        # 获取文件路径
        file_paths = [self.tree.item(item, "values")[4] for item in selected]
        
        # 确认标记
        if len(file_paths) == 1:
            if not messagebox.askyesno("确认标记", f"确定要将文件 {os.path.basename(file_paths[0])} 标记为垃圾文件吗？"):
                return
        else:
            if not messagebox.askyesno("确认标记", f"确定要将选中的 {len(file_paths)} 个文件标记为垃圾文件吗？"):
                return
        
        # 标记为垃圾文件
        try:
            for file_path in file_paths:
                # 更新数据库
                if self.main_window.db_manager:
                    self.main_window.db_manager.update_file_info(file_path, {"is_junk": True, "is_whitelist": False})
                
                # 更新文件信息
                if file_path in self.files:
                    self.files[file_path]["is_junk"] = True
                    self.files[file_path]["is_whitelist"] = False
            
            # 刷新文件树
            self.filter_tree()
            
            # 显示提示
            if len(file_paths) == 1:
                self.main_window.log_message(f"已将文件 {os.path.basename(file_paths[0])} 标记为垃圾文件", "success")
            else:
                self.main_window.log_message(f"已将 {len(file_paths)} 个文件标记为垃圾文件", "success")
        except Exception as e:
            messagebox.showerror("错误", f"标记为垃圾文件失败: {str(e)}")
            self.logger.error(f"标记为垃圾文件失败, 错误: {str(e)}")
    
    def batch_set_whitelist_type(self, type_value):
        """
        批量设置白名单类型（CH/EN/其他），包括选中文件及其子文件夹下所有文件
        """
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件或文件夹")
            return
        # 获取所有目标文件（包括子文件夹下所有文件）
        def collect_files(item):
            files = []
            values = self.tree.item(item, "values")
            tags = self.tree.item(item, "tags")
            if "folder" in tags:
                # 递归收集子项
                for child in self.tree.get_children(item):
                    files.extend(collect_files(child))
            else:
                if len(values) >= 5:
                    files.append(values[4])
            return files
        all_files = []
        for item in selected:
            all_files.extend(collect_files(item))
        if not all_files:
            messagebox.showwarning("警告", "未找到可设置的文件")
            return
        # 确认操作
        if not messagebox.askyesno("批量设置白名单类型", f"确定要将 {len(all_files)} 个文件设置为 {type_value} 类型白名单吗？"):
            return
        # 执行批量设置
        try:
            for file_path in all_files:
                if self.main_window.db_manager:
                    self.main_window.db_manager.update_file_info(file_path, {"is_whitelist": True, "is_junk": False, "whitelist_type": type_value})
                if file_path in self.files:
                    self.files[file_path]["is_whitelist"] = True
                    self.files[file_path]["is_junk"] = False
                    self.files[file_path]["whitelist_type"] = type_value
            self.filter_tree()
            self.main_window.log_message(f"已批量设置 {len(all_files)} 个文件为 {type_value} 类型白名单", "success")
        except Exception as e:
            messagebox.showerror("错误", f"批量设置白名单类型失败: {str(e)}")
            self.logger.error(f"批量设置白名单类型失败, 错误: {str(e)}")

    def set_file_type(self, file_type):
        """
        设置选中文件/文件夹的类型
        
        参数:
            file_type: 文件类型（JP/CH/ENG）
        """
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个文件或文件夹")
            return
        
        # 获取所有选中的文件/文件夹路径
        folder_paths = []
        for item in selected:
            values = self.tree.item(item, "values")
            if values and len(values) >= 5:
                file_path = values[4]  # 文件路径在第5列
                # 如果是文件，获取其父文件夹
                if os.path.isfile(file_path):
                    folder_path = os.path.dirname(file_path)
                else:
                    folder_path = file_path
                
                if folder_path not in folder_paths:
                    folder_paths.append(folder_path)
        
        if not folder_paths:
            messagebox.showwarning("警告", "未能获取有效的文件夹路径")
            return
        
        # 确认设置
        confirm = messagebox.askyesno(
            "确认操作", 
            f"确定要将选中的 {len(folder_paths)} 个文件夹设置为 {file_type} 类型吗？\n"
            "这将同时设置文件夹下所有文件的类型。"
        )
        
        if not confirm:
            return
        
        # 调用FileScanner的批量设置方法
        try:
            # 获取FileScanner实例
            file_scanner = None
            if hasattr(self.main_window, 'file_scanner') and self.main_window.file_scanner:
                file_scanner = self.main_window.file_scanner
            else:
                # 尝试从依赖注入获取
                from src.core.dependency_injection import resolve
                from src.core.file_scanner import FileScanner
                file_scanner = resolve(FileScanner)
            
            if not file_scanner:
                raise ValueError("无法获取FileScanner实例")
            
            # 异步执行批量设置
            def do_set_type():
                result = file_scanner.batch_set_folder_type(folder_paths, file_type)
                
                # 在主线程中显示结果
                def show_result():
                    if result["success"] > 0:
                        messagebox.showinfo(
                            "操作完成", 
                            f"成功设置 {result['success']} 个文件夹为 {file_type} 类型。"
                            f"{result['failed']} 个文件夹设置失败。"
                        )
                        # 刷新文件树
                        self.refresh_file_tree()
                    else:
                        messagebox.showerror(
                            "操作失败", 
                            f"设置文件类型失败，请检查日志。"
                        )
                
                # 在主线程中执行UI更新
                self.tree.after(0, show_result)
            
            # 在后台线程中执行设置操作
            threading.Thread(target=do_set_type).start()
            
        except Exception as e:
            self.logger.error(f"设置文件类型失败: {e}")
            messagebox.showerror("错误", f"设置文件类型失败: {e}")

    def update_stats(self, stats):
        """更新统计信息，刷新顶部统计标签"""
        from src.utils.format_utils import format_size
        stats_text = (
            f"文件总数: {stats.get('file_count', 0)}  "
            f"文件总大小: {format_size(stats.get('total_size', 0))}  "
            f"视频文件数: {stats.get('video_count', 0)}  "
            f"视频文件总大小: {format_size(stats.get('video_total_size', 0))}  "
            f"白名单文件数: {stats.get('whitelist_count', 0)}  "
            f"垃圾文件数: {stats.get('junk_count', 0)}"
        )
        if hasattr(self, 'top_stats_label') and self.top_stats_label:
            self.top_stats_label.config(text=stats_text)
            self.logger.info(f"已更新文件统计信息：{stats}")
        else:
            self.logger.warning("顶部统计标签不存在，无法更新统计信息")

    def calculate_file_stats(self):
        """
        计算当前文件树中的文件统计信息并刷新统计标签
        不依赖外部传入的统计数据，直接从files属性中计算
        """
        try:
            from src.utils.format_utils import format_size
            
            # 防止没有files属性
            if not hasattr(self, 'files'):
                self.logger.warning("计算文件统计信息失败：无files属性")
                return
            
            # 初始化统计数据
            stats = {
                'file_count': 0,
                'total_size': 0,
                'video_count': 0,
                'video_total_size': 0,
                'whitelist_count': 0,
                'junk_count': 0
            }
            
            # 统计文件数据
            for file_path, file_info in self.files.items():
                # 文件总数和总大小
                stats['file_count'] += 1
                file_size = file_info.get('size', 0)
                stats['total_size'] += file_size
                
                # 视频文件
                if file_info.get('is_video', False):
                    stats['video_count'] += 1
                    stats['video_total_size'] += file_size
                
                # 白名单文件
                if file_info.get('is_whitelist', False):
                    stats['whitelist_count'] += 1
                
                # 垃圾文件
                if file_info.get('is_junk', False):
                    stats['junk_count'] += 1
            
            # 更新统计标签
            self.update_stats(stats)
            return stats
            
        except Exception as e:
            self.logger.error(f"计算文件统计信息失败: {e}")
            return None

    def update_ui(self, data):
        """更新UI显示（占位方法，防止linter报错）"""
        pass

    def update_font_size(self, font_size: int) -> None:
        """更新字体大小"""
        try:
            # 更新树形视图样式
            row_height = max(25, font_size + 15)
            self.main_window.style.configure("Treeview", 
                                           rowheight=row_height,
                                           font=("Helvetica", font_size))
            self.main_window.style.configure("Treeview.Heading", 
                                           font=("Helvetica", font_size, "bold"))
            
            # 更新所有标签样式
            self.tree.tag_configure("folder", foreground="#0066cc", font=("Helvetica", font_size, "bold"))
            self.tree.tag_configure("video", foreground="blue", font=("Helvetica", font_size))
            self.tree.tag_configure("image", foreground="green", font=("Helvetica", font_size))
            self.tree.tag_configure("audio", foreground="purple", font=("Helvetica", font_size))
            self.tree.tag_configure("document", foreground="brown", font=("Helvetica", font_size))
            self.tree.tag_configure("archive", foreground="orange", font=("Helvetica", font_size))
            self.tree.tag_configure("junk", foreground="red", font=("Helvetica", font_size))
            self.tree.tag_configure("whitelist", foreground="darkgreen", font=("Helvetica", font_size))
            self.tree.tag_configure("duplicate", background="#ffe0e0", font=("Helvetica", font_size))
            
            # 刷新树形视图
            self.tree.update()
            
            if hasattr(self, 'logger'):
                self.logger.info(f"文件树字体大小已更新为: {font_size}")
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"更新文件树字体大小失败: {e}")
    
    def update_display_options(self, show_icons: bool = True, show_size: bool = True, show_time: bool = True) -> None:
        """
        更新文件树的显示选项（图标、大小、时间）
        
        参数:
            show_icons (bool): 是否显示图标
            show_size (bool): 是否显示文件大小
            show_time (bool): 是否显示文件修改时间
        """
        # 此方法的实现需要根据实际的树形视图配置来调整
        # 例如，如果使用ttk.Treeview，则需要重新配置列的显示
        # 如果使用ttk.Treeview.Column，则需要重新设置列的属性
        # 目前没有完整的实现，仅保留方法框架
        pass

    def destroy(self) -> None:
        """销毁组件"""
        try:
            # 取消订阅白名单规则变更事件
            try:
                from src.utils.event_system import get_event_system
                event_system = get_event_system()
                event_system.unsubscribe_all("file_tree_panel")
                self.logger.info("已取消订阅白名单规则变更事件")
            except Exception as e:
                self.logger.error(f"取消订阅白名单规则变更事件失败: {e}")
            
            # 销毁框架
            if hasattr(self, 'frame') and self.frame:
                self.frame.destroy()
                
        except Exception as e:
            self.logger.error(f"销毁文件树面板失败: {e}")

    def _cancel_refresh(self):
        """取消刷新操作"""
        try:
            self.refresh_cancelled = True
            self.refresh_in_progress = False
            
            # 更新UI状态
            self._update_refresh_ui_state(False)
            
            # 显示取消提示
            self._show_refresh_complete_message("刷新已取消")
            
            self.logger.info("用户取消了白名单状态刷新")
            
        except Exception as e:
            self.logger.error(f"取消刷新失败: {e}")
    
    def _update_refresh_ui_state(self, in_progress: bool):
        """更新刷新相关的UI状态"""
        try:
            if not hasattr(self, 'refresh_status_label') or not self.refresh_status_label:
                return
            if not hasattr(self, 'refresh_progress') or not self.refresh_progress:
                return
            if not hasattr(self, 'refresh_cancel_button') or not self.refresh_cancel_button:
                return
                
            if in_progress:
                # 刷新进行中
                self.refresh_status_label.config(text="正在刷新白名单状态...", foreground="blue")
                self.refresh_progress.start(10)
                self.refresh_cancel_button.config(state='normal')
                self.refresh_in_progress = True
            else:
                # 刷新完成或取消
                self.refresh_status_label.config(text="就绪", foreground="green")
                self.refresh_progress.stop()
                self.refresh_cancel_button.config(state='disabled')
                self.refresh_in_progress = False
                self.refresh_cancelled = False
                
        except Exception as e:
            self.logger.error(f"更新刷新UI状态失败: {e}")
    
    def _show_refresh_complete_message(self, message: str):
        """显示刷新完成消息"""
        try:
            if not hasattr(self, 'refresh_status_label') or not self.refresh_status_label:
                return
                
            # 临时显示完成消息
            self.refresh_status_label.config(text=message, foreground="green")
            
            # 3秒后恢复"就绪"状态
            if hasattr(self, 'frame') and self.frame:
                self.frame.after(3000, lambda: self.refresh_status_label.config(text="就绪", foreground="green") if self.refresh_status_label else None)
            
        except Exception as e:
            self.logger.error(f"显示刷新完成消息失败: {e}")
    
    def _show_refresh_progress(self, current: int, total: int):
        """显示刷新进度"""
        try:
            if not hasattr(self, 'refresh_status_label') or not self.refresh_status_label:
                return
                
            if total > 0:
                percentage = (current / total) * 100
                self.refresh_status_label.config(text=f"刷新进度: {current}/{total} ({percentage:.1f}%)", foreground="blue")
            else:
                self.refresh_status_label.config(text="正在刷新...", foreground="blue")
                
        except Exception as e:
            self.logger.error(f"显示刷新进度失败: {e}")

    def _on_folder_expand_lazy(self, event=None):
        """文件夹展开时懒加载子项 - 优化版本，使用异步处理"""
        try:
            selected = self.tree.focus()
            if not selected:
                return

            # 获取标准化路径
            folder_path = self._get_full_path_from_tree(selected)
            folder_path = _normalize_path(folder_path)

            if folder_path in self.lazy_loaded_folders:
                self.logger.info(f"[懒加载] 文件夹{folder_path}已加载过，跳过")
                return

            if folder_path in self.expanding_folders:
                self.logger.info(f"[懒加载] 文件夹{folder_path}正在加载中，跳过")
                return

            # 标记为正在展开
            self.expanding_folders.add(folder_path)

            # 移除占位符
            if selected in self.folder_placeholder_items:
                self.tree.delete(self.folder_placeholder_items[selected])
                del self.folder_placeholder_items[selected]

            self.logger.info(f"[懒加载] 开始异步加载文件夹{folder_path}的子项...")

            # 使用异步方式加载，避免阻塞UI
            self._schedule_lazy_loading(selected, folder_path)

        except Exception as e:
            self.logger.error(f"懒加载文件夹子项失败: {e}")
            # 清理状态
            if 'folder_path' in locals():
                self.expanding_folders.discard(folder_path)

    def _schedule_lazy_loading(self, parent_id, folder_path):
        """调度异步懒加载任务 - 真正异步版本"""
        try:
            import threading

            def run_lazy_loading():
                """在独立线程中运行懒加载"""
                try:
                    # 确保文件索引已构建
                    if not self.file_index_built:
                        self._build_file_index()

                    # 使用索引快速查找
                    parent_path = _normalize_path(folder_path)
                    direct_children = self.file_index_by_parent.get(parent_path, [])

                    # 查找子文件夹
                    subfolders_dict = {}
                    for indexed_parent, files in self.file_index_by_parent.items():
                        if indexed_parent.startswith(parent_path) and indexed_parent != parent_path:
                            rel_path = indexed_parent[len(parent_path):].strip('/\\')
                            if rel_path and '/' not in rel_path and '\\' not in rel_path:
                                subfolder_name = rel_path
                                subfolders_dict[subfolder_name.lower()] = indexed_parent

                    # 在主线程中更新UI
                    self.frame.after(0, lambda: self._update_tree_with_children(
                        parent_id, parent_path, direct_children, subfolders_dict
                    ))

                except Exception as e:
                    self.logger.error(f"懒加载执行失败: {e}")
                    # 在主线程中清理状态
                    self.frame.after(0, lambda: self.expanding_folders.discard(folder_path))

            # 启动独立线程执行懒加载
            thread = threading.Thread(target=run_lazy_loading, daemon=True, name=f"LazyLoad-{folder_path}")
            thread.start()

        except Exception as e:
            self.logger.error(f"调度懒加载任务失败: {e}")
            self.expanding_folders.discard(folder_path)

    def _update_tree_with_children(self, parent_id, parent_path, direct_children, subfolders_dict):
        """在主线程中更新树结构（UI安全）"""
        try:
            # 更新状态栏
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(50, f"加载 {os.path.basename(parent_path)} ...")

            # 插入子文件夹
            for subfolder_name_lower, full_path in subfolders_dict.items():
                try:
                    actual_folder_name = os.path.basename(full_path)

                    # 检查是否已存在
                    folder_exists = False
                    for child_id in self.tree.get_children(parent_id):
                        child_values = self.tree.item(child_id, 'values')
                        if child_values and len(child_values) >= 5:
                            child_path = _normalize_path(child_values[4])
                            if child_path.lower() == full_path.lower():
                                folder_exists = True
                                break

                    if not folder_exists:
                        # 插入子文件夹节点
                        subfolder_node = self.tree.insert(
                            parent_id, "end",
                            text=actual_folder_name,
                            values=(actual_folder_name, "", "文件夹", "", full_path),
                            tags=("folder", "directory"),
                            open=False
                        )

                        # 添加占位符
                        placeholder = self.tree.insert(
                            subfolder_node, "end",
                            text="加载中...",
                            values=("加载中...", "", "", "", "(加载中...)"),
                            tags=("placeholder",)
                        )
                        self.folder_placeholder_items[subfolder_node] = placeholder

                except Exception as e:
                    self.logger.error(f"插入子文件夹失败: {e}")

            # 插入文件
            for file_path, file_info in direct_children:
                try:
                    self.add_file_to_tree(parent_id, file_path, file_info)
                except Exception as e:
                    self.logger.error(f"插入文件失败: {e}")

            # 完成加载
            self.lazy_loaded_folders.add(parent_path)
            self.expanding_folders.discard(parent_path)

            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(100, f"{os.path.basename(parent_path)} 加载完成")

            self.logger.info(f"[懒加载] 文件夹{parent_path}子项加载完成")

        except Exception as e:
            self.logger.error(f"更新树结构失败: {e}")
            self.expanding_folders.discard(parent_path)

    def _load_folder_children_lazy_optimized(self, parent_id, folder_path):
        """优化的懒加载方法，使用文件索引和批量处理"""
        try:
            # 确保文件索引已构建
            if not self.file_index_built:
                self._build_file_index()

            parent_path = _normalize_path(folder_path)
            self.logger.info(f"[懒加载] 开始优化加载: {parent_path}")

            # 更新状态栏
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(10, f"正在加载 {os.path.basename(parent_path)} ...")

            # 使用索引快速查找直接子文件
            direct_children = self.file_index_by_parent.get(parent_path, [])

            # 查找子文件夹
            subfolders_dict = {}
            for file_path, file_info in direct_children:
                try:
                    # 这些已经是直接子文件，无需复杂路径计算
                    pass  # 直接子文件处理在后面
                except Exception as e:
                    self.logger.error(f"处理直接子文件失败: {e}")

            # 查找子文件夹（通过检查所有文件的父路径）
            for indexed_parent, files in self.file_index_by_parent.items():
                if indexed_parent.startswith(parent_path) and indexed_parent != parent_path:
                    # 计算相对路径
                    rel_path = indexed_parent[len(parent_path):].strip('/\\')
                    if rel_path and '/' not in rel_path and '\\' not in rel_path:
                        # 这是直接子文件夹
                        subfolder_name = rel_path
                        subfolders_dict[subfolder_name.lower()] = indexed_parent

            self.logger.info(f"[懒加载] {parent_path} 找到 {len(direct_children)} 个文件, {len(subfolders_dict)} 个子文件夹")

            # 启动批量插入
            self._insert_children_batch(parent_id, parent_path, direct_children, subfolders_dict)

        except Exception as e:
            self.logger.error(f"优化懒加载失败: {e}, 文件夹: {folder_path}")
            self.expanding_folders.discard(folder_path)

    def _insert_children_batch(self, parent_id, parent_path, direct_children, subfolders_dict):
        """批量插入子项，使用异步处理避免UI阻塞"""
        try:
            # 初始化批量插入状态
            self.batch_insert_state = {
                'parent_id': parent_id,
                'parent_path': parent_path,
                'direct_children': direct_children,
                'subfolders_dict': subfolders_dict,
                'folder_index': 0,
                'file_index': 0,
                'total_items': len(direct_children) + len(subfolders_dict),
                'processed_items': 0
            }

            # 开始批量插入
            self._process_next_children_batch()

        except Exception as e:
            self.logger.error(f"批量插入初始化失败: {e}")
            self.expanding_folders.discard(parent_path)

    def _process_next_children_batch(self):
        """处理下一批子项插入"""
        try:
            state = self.batch_insert_state
            batch_size = 10  # 每批处理10个项目
            processed_count = 0

            # 处理子文件夹
            subfolder_items = list(state['subfolders_dict'].items())
            while state['folder_index'] < len(subfolder_items) and processed_count < batch_size:
                subfolder_name_lower, full_path = subfolder_items[state['folder_index']]
                try:
                    actual_folder_name = os.path.basename(full_path)

                    # 检查是否已存在
                    folder_exists = False
                    for child_id in self.tree.get_children(state['parent_id']):
                        child_values = self.tree.item(child_id, 'values')
                        if child_values and len(child_values) >= 5:
                            child_path = _normalize_path(child_values[4])
                            if child_path.lower() == full_path.lower():
                                folder_exists = True
                                break

                    if not folder_exists:
                        # 插入子文件夹节点
                        subfolder_node = self.tree.insert(
                            state['parent_id'], "end",
                            text=actual_folder_name,
                            values=(actual_folder_name, "", "文件夹", "", full_path),
                            tags=("folder", "directory"),
                            open=False
                        )

                        # 添加占位符
                        placeholder = self.tree.insert(
                            subfolder_node, "end",
                            text="加载中...",
                            values=("加载中...", "", "", "", "(加载中...)"),
                            tags=("placeholder",)
                        )
                        self.folder_placeholder_items[subfolder_node] = placeholder

                except Exception as e:
                    self.logger.error(f"插入子文件夹失败: {e}")

                state['folder_index'] += 1
                processed_count += 1
                state['processed_items'] += 1

            # 处理文件
            while state['file_index'] < len(state['direct_children']) and processed_count < batch_size:
                file_path, file_info = state['direct_children'][state['file_index']]
                try:
                    self.add_file_to_tree(state['parent_id'], file_path, file_info)
                except Exception as e:
                    self.logger.error(f"插入文件失败: {e}")

                state['file_index'] += 1
                processed_count += 1
                state['processed_items'] += 1

            # 更新进度
            if state['total_items'] > 0:
                progress = 10 + (state['processed_items'] / state['total_items']) * 80
                if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.update_progress(
                        progress,
                        f"加载中... {state['processed_items']}/{state['total_items']}"
                    )

            # 检查是否完成
            if state['folder_index'] >= len(subfolder_items) and state['file_index'] >= len(state['direct_children']):
                # 完成加载
                self._finish_lazy_loading(state['parent_path'])
            else:
                # 调度下一批，使用较短间隔保持响应性
                self.frame.after(5, self._process_next_children_batch)

        except Exception as e:
            self.logger.error(f"批量插入处理失败: {e}")
            if hasattr(self, 'batch_insert_state'):
                self._finish_lazy_loading(self.batch_insert_state['parent_path'])

    def _finish_lazy_loading(self, parent_path):
        """完成懒加载"""
        try:
            self.lazy_loaded_folders.add(parent_path)
            self.expanding_folders.discard(parent_path)

            if hasattr(self, 'main_window') and hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.update_progress(100, f"{os.path.basename(parent_path)} 加载完成")

            self.logger.info(f"[懒加载] 文件夹{parent_path}子项加载完成")

            # 清理状态
            if hasattr(self, 'batch_insert_state'):
                del self.batch_insert_state

        except Exception as e:
            self.logger.error(f"完成懒加载失败: {e}")

    def _load_folder_children_lazy(self, parent_id, folder_path=None):
        """加载指定文件夹的子项（自动生成子文件夹节点）"""
        try:
            import os
            # 确保使用规范化路径
            if folder_path is None:
                parent_path = self._get_full_path_from_tree(parent_id)
                parent_path = _normalize_path(parent_path)
            else:
                parent_path = _normalize_path(folder_path)
                
            # 记录原始路径
            original_parent_path = parent_path
            if original_parent_path != parent_path:
                self.logger.debug(f'[懒加载] 父路径标准化: {original_parent_path} -> {parent_path}')
                
            # 1. 找出所有直接子文件
            children_files = []
            # 使用集合，确保文件夹名称不重复
            subfolders_dict = {}  # {folder_name: full_path}
            
            for file_path, file_info in self.files.items():
                normalized_file_path = _normalize_path(file_path)
                dir_path = os.path.dirname(normalized_file_path)
                
                # 调试详细信息
                self.logger.debug(f'[懒加载] 比较路径: dir_path={dir_path}, parent_path={parent_path}')
                
                # 直接子文件
                if dir_path.lower() == parent_path.lower():  # 大小写不敏感比较
                    children_files.append((normalized_file_path, file_info))
                    continue
                
                # 子目录处理
                try:
                    # 计算相对路径，查找子文件夹
                    if not normalized_file_path.startswith(parent_path):
                        continue
                        
                    rel_path = normalized_file_path[len(parent_path):].strip('/\\')
                    parts = rel_path.split('/')
                    
                    if len(parts) > 1 and parts[0]:
                        subfolder_name = parts[0]
                        subfolder_path = os.path.join(parent_path, subfolder_name).replace('\\', '/')
                        # 保存完整子文件夹路径，确保唯一性
                        subfolders_dict[subfolder_name.lower()] = subfolder_path
                except Exception as e:
                    self.logger.error(f"[懒加载] 处理子文件夹时出错: {e}, 文件路径: {normalized_file_path}")
            
            # 输出调试信息：直接子文件和子文件夹
            self.logger.info(f"[懒加载] {parent_path} 直接子项数: {len(children_files) + len(subfolders_dict)}")
            
            # 2. 插入所有直接子文件夹节点（用标准化路径为key防止重复）
            folder_nodes = {}  # {full_path: tree_node_id}
            
            # 先处理子文件夹
            for subfolder_name_lower, full_path in sorted(subfolders_dict.items()):
                try:
                    # 从完整路径中获取实际文件夹名（而非小写版本）
                    actual_folder_name = os.path.basename(full_path)
                    
                    # 检查子文件夹是否已经存在于树中
                    subfolder_exists = False
                    for child_id in self.tree.get_children(parent_id):
                        child_values = self.tree.item(child_id, 'values')
                        if child_values and len(child_values) >= 5:
                            child_path = _normalize_path(child_values[4])
                            if child_path.lower() == full_path.lower():
                                subfolder_exists = True
                                folder_nodes[full_path] = child_id
                                self.logger.info(f"[懒加载] 子文件夹已存在: {actual_folder_name} ({full_path})")
                                break
                    
                    # 如果子文件夹不存在，则创建
                    if not subfolder_exists:
                        # 插入子文件夹节点
                        subfolder_node = self.tree.insert(
                            parent_id, "end",
                            text=actual_folder_name,
                            values=(actual_folder_name, "", "文件夹", "", full_path),
                            tags=("folder", "directory"),
                            open=False
                        )
                        folder_nodes[full_path] = subfolder_node
                        
                        # 添加占位符子节点，显示展开图标
                        placeholder = self.tree.insert(
                            subfolder_node, "end",
                            text="加载中...",
                            values=("加载中...", "", "", "", "(加载中...)"),
                            tags=("placeholder",)
                        )
                        self.folder_placeholder_items[subfolder_node] = placeholder
                        self.logger.info(f"[懒加载] 已插入子文件夹节点: {actual_folder_name} ({full_path})")
                except Exception as e:
                    self.logger.error(f"[懒加载] 插入子文件夹节点出错: {e}, 文件夹: {full_path}")
            
            # 3. 插入所有直接子文件
            for file_path, file_info in children_files:
                try:
                    self.add_file_to_tree(parent_id, file_path, file_info)
                except Exception as e:
                    self.logger.error(f"[懒加载] 插入子文件节点出错: {e}, 文件: {file_path}")
            
        except Exception as e:
            self.logger.error(f"懒加载文件夹子项失败: {e}, 文件夹ID: {parent_id}, 路径: {folder_path}")

    def _get_full_path_from_tree(self, item_id):
        """递归获取树节点的完整路径"""
        path_parts = []
        while item_id:
            try:
                text = self.tree.item(item_id, 'text')
                # 确保text是字符串
                if isinstance(text, dict):
                    text = text.get('text', '')
                elif not isinstance(text, str):
                    text = str(text)
                path_parts.insert(0, text)
                item_id = self.tree.parent(item_id)
            except Exception as e:
                self.logger.error(f"获取树节点路径时出错: {e}")
                break
        # 使用os.sep连接路径，然后标准化
        full_path = os.sep.join(path_parts)
        normalized_path = _normalize_path(full_path)
        self.logger.debug(f'[文件树] 从树节点获取路径: {full_path} -> {normalized_path}')
        return normalized_path

    def _insert_folder_node(self, parent, folder_path, display_name):
        node_id = self.tree.insert(
            parent, "end",
            text=display_name,
            values=(display_name, "", "文件夹", "", folder_path),
            tags=("folder", "directory"),
            open=False
        )
        # 插入占位符子节点
        placeholder_id = self.tree.insert(node_id, "end", text="加载中...", values=("", "", "", "", ""))
        self.folder_placeholder_items[node_id] = placeholder_id
        return node_id

    def _on_folder_expand_lazy(self, event=None):
        item_id = self.tree.focus()
        if item_id in self.folder_placeholder_items:
            self.tree.delete(self.folder_placeholder_items[item_id])
            del self.folder_placeholder_items[item_id]
            folder_path = self.tree.item(item_id, 'values')[4]
            self._load_folder_children_lazy(item_id, folder_path)

    def _load_folder_children_lazy(self, parent_id, folder_path=None):
        # 查询数据库/缓存获取folder_path下的文件和子文件夹
        # 这里假设有db_manager实例和find_files接口
        db_manager = getattr(self.main_window, 'db_manager', None)
        if not db_manager or not folder_path:
            return
        # 只查直接子节点（depth+1，parent_id=当前文件夹）
        parent_info = db_manager.collection.find_one({'path': folder_path})
        if not parent_info:
            return
        parent_depth = parent_info.get('depth', 1)
        children = list(db_manager.collection.find({'parent_id': parent_info.get('file_id'), 'depth': parent_depth + 1}))
        for child in children:
            if child.get('is_dir'):
                self._insert_folder_node(parent_id, child['path'], child['name'])
            else:
                self.add_file_to_tree(parent_id, child['path'], child)
        # 预加载下一级（可选，异步后台）
        self._preload_next_layer(children, parent_depth + 2)

    def _preload_next_layer(self, children, next_depth):
        # 预加载下一级目录数据，缓存到self.folder_children_cache
        db_manager = getattr(self.main_window, 'db_manager', None)
        if not db_manager:
            return
        folder_ids = [c['file_id'] for c in children if c.get('is_dir')]
        for folder_id in folder_ids:
            sub_children = list(db_manager.collection.find({'parent_id': folder_id, 'depth': next_depth}))
            self.folder_children_cache[folder_id] = sub_children

    


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600")
    
    # 创建主窗口模拟对象
    class MainWindowMock:
        def __init__(self):
            self.root = root
            self.style = ttk.Style()
            self.db_manager = None
            self.status_text = tk.StringVar()
            self.event_system = None
    
    main_window = MainWindowMock()
    
    # 创建文件树面板
    panel = FileTreePanel(root, main_window, get_logger('FileTreePanel'))
    panel.frame.pack(fill=tk.BOTH, expand=True)


    
    root.mainloop()