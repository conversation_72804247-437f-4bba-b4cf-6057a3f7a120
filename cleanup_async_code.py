#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步代码清理脚本

清理项目中遗留的混合异步模式代码和优化错误处理

作者: AI助手
日期: 2025-01-30
"""

import os
import re
import ast
from typing import List, Dict, Tuple
from pathlib import Path


class AsyncCodeCleaner:
    """异步代码清理器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.cleaned_files = []
        self.issues_found = []
    
    def clean_project(self):
        """清理整个项目"""
        print("🧹 开始清理异步代码...")
        
        # 清理遗留的asyncio.run()调用
        self._clean_asyncio_run_calls()
        
        # 清理直接的ThreadPoolExecutor使用
        self._clean_direct_threadpool_usage()
        
        # 清理注释掉的代码
        self._clean_commented_code()
        
        # 优化错误处理
        self._optimize_error_handling()
        
        # 生成清理报告
        self._generate_cleanup_report()
    
    def _clean_asyncio_run_calls(self):
        """清理遗留的asyncio.run()调用"""
        print("🔍 检查遗留的asyncio.run()调用...")
        
        # 在async_manager.py中的测试代码
        async_manager_path = os.path.join(self.project_root, "src/utils/async_manager.py")
        if os.path.exists(async_manager_path):
            self._clean_test_code_in_async_manager(async_manager_path)
        
        # 在file_operations.py和file_scanner.py中的回退方案
        for file_name in ["file_operations.py", "file_scanner.py"]:
            file_path = os.path.join(self.project_root, f"src/core/{file_name}")
            if os.path.exists(file_path):
                self._optimize_fallback_asyncio_run(file_path)
    
    def _clean_test_code_in_async_manager(self, file_path: str):
        """清理async_manager.py中的测试代码"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找测试代码块
            test_code_pattern = r'\n\n# 测试代码.*?asyncio\.run\(main\(\)\).*?$'
            
            if re.search(test_code_pattern, content, re.DOTALL | re.MULTILINE):
                # 移除测试代码
                new_content = re.sub(test_code_pattern, '', content, flags=re.DOTALL | re.MULTILINE)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.cleaned_files.append(file_path)
                self.issues_found.append(f"移除了{file_path}中的测试代码")
                print(f"✅ 清理了{file_path}中的测试代码")
        
        except Exception as e:
            print(f"⚠️ 清理{file_path}失败: {e}")
    
    def _optimize_fallback_asyncio_run(self, file_path: str):
        """优化回退方案中的asyncio.run()使用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找并优化回退方案
            fallback_pattern = r'(\s+)# 回退到asyncio\.run\(\)\s*\n\s+logger\.warning\("AsyncManager不可用，使用asyncio\.run\(\)回退方案"\)\s*\n\s+return asyncio\.run\(coro\)'
            
            optimized_fallback = r'\1# 回退到asyncio.run()\n\1logger.warning("AsyncManager不可用，使用asyncio.run()回退方案")\n\1try:\n\1    return asyncio.run(coro)\n\1except RuntimeError as e:\n\1    logger.error(f"asyncio.run()回退方案失败: {e}")\n\1    raise'
            
            new_content = re.sub(fallback_pattern, optimized_fallback, content)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.cleaned_files.append(file_path)
                self.issues_found.append(f"优化了{file_path}中的回退方案错误处理")
                print(f"✅ 优化了{file_path}中的回退方案")
        
        except Exception as e:
            print(f"⚠️ 优化{file_path}失败: {e}")
    
    def _clean_direct_threadpool_usage(self):
        """清理直接的ThreadPoolExecutor使用"""
        print("🔍 检查直接的ThreadPoolExecutor使用...")
        
        # 检查video_analyzer.py
        video_analyzer_path = os.path.join(self.project_root, "src/core/video_analyzer.py")
        if os.path.exists(video_analyzer_path):
            self._optimize_video_analyzer_threadpool(video_analyzer_path)
        
        # 检查file_operations_service.py
        service_path = os.path.join(self.project_root, "src/services/implementations/file_operations_service.py")
        if os.path.exists(service_path):
            self._check_service_threadpool_usage(service_path)
    
    def _optimize_video_analyzer_threadpool(self, file_path: str):
        """优化video_analyzer.py中的ThreadPoolExecutor使用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否使用了ThreadPoolExecutor
            if 'ThreadPoolExecutor' in content:
                # 添加注释说明为什么这里需要使用ThreadPoolExecutor
                comment_pattern = r'(\s+# 创建线程池\s*\n\s+self\.thread_pool = ThreadPoolExecutor\(max_workers=thread_pool_size\))'
                
                optimized_comment = r'\1\n        # 注意: 视频分析需要使用专用线程池，因为涉及到外部库调用\n        # 这里不使用AsyncManager是为了避免与主要异步流程冲突'
                
                new_content = re.sub(comment_pattern, optimized_comment, content)
                
                if new_content != content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    self.issues_found.append(f"为{file_path}中的ThreadPoolExecutor使用添加了说明注释")
                    print(f"✅ 为{file_path}添加了ThreadPoolExecutor使用说明")
        
        except Exception as e:
            print(f"⚠️ 处理{file_path}失败: {e}")
    
    def _check_service_threadpool_usage(self, file_path: str):
        """检查服务中的ThreadPoolExecutor使用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'ThreadPoolExecutor' in content:
                self.issues_found.append(f"发现{file_path}中使用了ThreadPoolExecutor，建议评估是否可以使用AsyncManager")
                print(f"⚠️ {file_path}中使用了ThreadPoolExecutor，建议评估")
        
        except Exception as e:
            print(f"⚠️ 检查{file_path}失败: {e}")
    
    def _clean_commented_code(self):
        """清理注释掉的代码"""
        print("🔍 检查注释掉的代码...")
        
        # 检查async_task_manager.py中的注释代码
        async_task_manager_path = os.path.join(self.project_root, "src/utils/async_task_manager.py")
        if os.path.exists(async_task_manager_path):
            self._clean_commented_code_in_file(async_task_manager_path)
    
    def _clean_commented_code_in_file(self, file_path: str):
        """清理文件中的注释代码"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找大块的注释代码
            commented_code_pattern = r'\n\s*# 取消所有任务\s*\n\s*# 注意：由于我们不再管理事件循环，这里的取消逻辑需要调整\s*\n\s*# for task_id in list\(self\.tasks\.keys\(\)\):\s*\n\s*#\s+asyncio\.run\(self\.interrupt_task\(task_id\)\)\s*\n'
            
            new_content = re.sub(commented_code_pattern, '\n        # TODO: 实现任务取消逻辑\n', content)
            
            if new_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.cleaned_files.append(file_path)
                self.issues_found.append(f"清理了{file_path}中的注释代码")
                print(f"✅ 清理了{file_path}中的注释代码")
        
        except Exception as e:
            print(f"⚠️ 清理{file_path}失败: {e}")
    
    def _optimize_error_handling(self):
        """优化错误处理"""
        print("🔍 优化错误处理...")
        
        # 检查main_window.py中的错误处理
        main_window_path = os.path.join(self.project_root, "src/ui/main_window.py")
        if os.path.exists(main_window_path):
            self._optimize_main_window_error_handling(main_window_path)
    
    def _optimize_main_window_error_handling(self, file_path: str):
        """优化主窗口的错误处理"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找可能需要优化的错误处理
            if 'asyncio.run(' in content:
                self.issues_found.append(f"发现{file_path}中仍有asyncio.run()调用，建议检查是否可以优化")
                print(f"⚠️ {file_path}中仍有asyncio.run()调用")
        
        except Exception as e:
            print(f"⚠️ 检查{file_path}失败: {e}")
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        print("\n" + "="*80)
        print("异步代码清理报告")
        print("="*80)
        
        print(f"清理的文件数: {len(self.cleaned_files)}")
        print(f"发现的问题数: {len(self.issues_found)}")
        
        if self.cleaned_files:
            print(f"\n✅ 已清理的文件:")
            for file_path in self.cleaned_files:
                rel_path = os.path.relpath(file_path, self.project_root)
                print(f"  - {rel_path}")
        
        if self.issues_found:
            print(f"\n📋 发现的问题和改进:")
            for issue in self.issues_found:
                print(f"  - {issue}")
        
        if not self.cleaned_files and not self.issues_found:
            print("\n✅ 代码已经很干净，无需清理")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    project_root = "."  # 项目根目录
    
    cleaner = AsyncCodeCleaner(project_root)
    cleaner.clean_project()


if __name__ == "__main__":
    main()
